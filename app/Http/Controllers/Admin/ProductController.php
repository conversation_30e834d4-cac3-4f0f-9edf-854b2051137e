<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyProductRequest;
use App\Http\Requests\StoreProductRequest;
use App\Http\Requests\UpdateProductRequest;
use App\Models\Country;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductTag;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\Project;
use App\Models\Service;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use Yajra\DataTables\Facades\DataTables;
use App\Models\PriceList;

class ProductController extends Controller
{
    use CsvImportTrait;
    use MediaUploadingTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('product_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = Product::with(['product_category', 'product_tags', 'supplier', 'country', 'currency', 'team', 'store'])->select(sprintf('%s.*', (new Product)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'product_show';
                $editGate      = 'product_edit';
                $deleteGate    = 'product_delete';
                $crudRoutePart = 'products';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('product_code', function ($row) {
                return $row->product_code ? $row->product_code : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? '<a href="' . route('admin.products.show', $row->id) . '" class="badge bg-primary text-white">' . $row->name . '</a>' : '';
            });
            $table->addColumn('product_category_title', function ($row) {
                return $row->product_category ? $row->product_category->title : '';
            });

            $table->editColumn('product_tag', function ($row) {
                $labels = [];
                foreach ($row->product_tags as $product_tag) {
                    $labels[] = sprintf('<span class="label label-info label-many">%s</span>', $product_tag->title);
                }

                return implode(' ', $labels);
            });
            $table->editColumn('available_stock_level', function ($row) {
                return $row->available_stock_level ? $row->available_stock_level : '';
            });
            $table->addColumn('supplier_company_type', function ($row) {
                return $row->supplier ? $row->supplier->company_type : '';
            });

            $table->addColumn('country_name', function ($row) {
                return $row->country ? $row->country->name : '';
            });

            $table->addColumn('currency_name', function ($row) {
                return $row->currency ? $row->currency->name : '';
            });

            $table->editColumn('rate_of_exchange', function ($row) {
                return $row->rate_of_exchange ? $row->rate_of_exchange : '';
            });
            $table->editColumn('unit_buying_in_currency', function ($row) {
                return $row->unit_buying_in_currency ? $row->unit_buying_in_currency : '';
            });
            $table->editColumn('unit_buying_local_price', function ($row) {
                return $row->unit_buying_local_price ? $row->unit_buying_local_price : '';
            });
            $table->editColumn('charges', function ($row) {
                return $row->charges ? $row->charges : '';
            });
            $table->editColumn('shipping_charges', function ($row) {
                return $row->shipping_charges ? $row->shipping_charges : '';
            });
            $table->editColumn('clearence', function ($row) {
                return $row->clearence ? $row->clearence : '';
            });
            $table->editColumn('clearence_type', function ($row) {
                return $row->clearence_type ? Product::CLEARENCE_TYPE_RADIO[$row->clearence_type] : '';
            });
            $table->editColumn('duties', function ($row) {
                return $row->duties ? $row->duties : '';
            });
            $table->editColumn('duties_type', function ($row) {
                return $row->duties_type ? Product::DUTIES_TYPE_RADIO[$row->duties_type] : '';
            });
            $table->editColumn('delivery_charges', function ($row) {
                return $row->delivery_charges ? $row->delivery_charges : '';
            });
            $table->editColumn('net_price', function ($row) {
                return $row->net_price ? $row->net_price : '';
            });
            $table->editColumn('discount_rebate_refund', function ($row) {
                return $row->discount_rebate_refund ? $row->discount_rebate_refund : '';
            });
            $table->editColumn('discount_rebate_refund_type', function ($row) {
                return $row->discount_rebate_refund_type ? Product::DISCOUNT_REBATE_REFUND_TYPE_RADIO[$row->discount_rebate_refund_type] : '';
            });
            $table->editColumn('mark_up_price', function ($row) {
                return $row->mark_up_price ? $row->mark_up_price : '';
            });
            $table->editColumn('margin', function ($row) {
                return $row->margin ? $row->margin : '';
            });
            $table->editColumn('mark_up_price_type', function ($row) {
                return $row->mark_up_price_type ? Product::MARK_UP_PRICE_TYPE_RADIO[$row->mark_up_price_type] : '';
            });
            $table->editColumn('margin_type', function ($row) {
                return $row->margin_type ? Product::MARGIN_TYPE_RADIO[$row->margin_type] : '';
            });
            $table->editColumn('net_selling_price', function ($row) {
                return $row->net_selling_price ? $row->net_selling_price : '';
            });
            $table->editColumn('brand', function ($row) {
                return $row->brand ? $row->brand : '';
            });
            $table->addColumn('store_title', function ($row) {
                return $row->store ? $row->store->title : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'product_category', 'product_tag', 'supplier', 'country', 'currency', 'store','name']);

            return $table->make(true);
        }

        return view('admin.products.index');
    }

    public function process(Request $request)
    {
        abort_if(Gate::denies('product_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = Product::where('status', '1')->with(['product_category', 'product_tags', 'supplier', 'country', 'currency', 'team', 'store'])->select(sprintf('%s.*', (new Product)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'product_show';
                $editGate      = 'product_edit';
                $deleteGate    = 'product_delete';
                $crudRoutePart = 'products';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('product_code', function ($row) {
                return $row->product_code ? $row->product_code : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? '<a href="' . route('admin.products.show', $row->id) . '" class="badge bg-primary text-white">' . $row->name . '</a>' : '';
            });
            $table->addColumn('product_category_title', function ($row) {
                return $row->product_category ? $row->product_category->title : '';
            });

            $table->editColumn('product_tag', function ($row) {
                $labels = [];
                foreach ($row->product_tags as $product_tag) {
                    $labels[] = sprintf('<span class="label label-info label-many">%s</span>', $product_tag->title);
                }

                return implode(' ', $labels);
            });
            $table->editColumn('available_stock_level', function ($row) {
                return $row->available_stock_level ? $row->available_stock_level : '';
            });
            $table->addColumn('supplier_company_type', function ($row) {
                return $row->supplier ? $row->supplier->company_type : '';
            });

            $table->addColumn('country_name', function ($row) {
                return $row->country ? $row->country->name : '';
            });

            $table->addColumn('currency_name', function ($row) {
                return $row->currency ? $row->currency->name : '';
            });

            $table->editColumn('rate_of_exchange', function ($row) {
                return $row->rate_of_exchange ? $row->rate_of_exchange : '';
            });
            $table->editColumn('unit_buying_in_currency', function ($row) {
                return $row->unit_buying_in_currency ? $row->unit_buying_in_currency : '';
            });
            $table->editColumn('unit_buying_local_price', function ($row) {
                return $row->unit_buying_local_price ? $row->unit_buying_local_price : '';
            });
            $table->editColumn('charges', function ($row) {
                return $row->charges ? $row->charges : '';
            });
            $table->editColumn('shipping_charges', function ($row) {
                return $row->shipping_charges ? $row->shipping_charges : '';
            });
            $table->editColumn('clearence', function ($row) {
                return $row->clearence ? $row->clearence : '';
            });
            $table->editColumn('clearence_type', function ($row) {
                return $row->clearence_type ? Product::CLEARENCE_TYPE_RADIO[$row->clearence_type] : '';
            });
            $table->editColumn('duties', function ($row) {
                return $row->duties ? $row->duties : '';
            });
            $table->editColumn('duties_type', function ($row) {
                return $row->duties_type ? Product::DUTIES_TYPE_RADIO[$row->duties_type] : '';
            });
            $table->editColumn('delivery_charges', function ($row) {
                return $row->delivery_charges ? $row->delivery_charges : '';
            });
            $table->editColumn('net_price', function ($row) {
                return $row->net_price ? $row->net_price : '';
            });
            $table->editColumn('discount_rebate_refund', function ($row) {
                return $row->discount_rebate_refund ? $row->discount_rebate_refund : '';
            });
            $table->editColumn('discount_rebate_refund_type', function ($row) {
                return $row->discount_rebate_refund_type ? Product::DISCOUNT_REBATE_REFUND_TYPE_RADIO[$row->discount_rebate_refund_type] : '';
            });
            $table->editColumn('mark_up_price', function ($row) {
                return $row->mark_up_price ? $row->mark_up_price : '';
            });
            $table->editColumn('margin', function ($row) {
                return $row->margin ? $row->margin : '';
            });
            $table->editColumn('mark_up_price_type', function ($row) {
                return $row->mark_up_price_type ? Product::MARK_UP_PRICE_TYPE_RADIO[$row->mark_up_price_type] : '';
            });
            $table->editColumn('margin_type', function ($row) {
                return $row->margin_type ? Product::MARGIN_TYPE_RADIO[$row->margin_type] : '';
            });
            $table->editColumn('net_selling_price', function ($row) {
                return $row->net_selling_price ? $row->net_selling_price : '';
            });
            $table->editColumn('brand', function ($row) {
                return $row->brand ? $row->brand : '';
            });
            $table->addColumn('store_title', function ($row) {
                return $row->store ? $row->store->title : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'product_category', 'product_tag', 'supplier', 'country', 'currency', 'store','name']);

            return $table->make(true);
        }

        return view('admin.products.process');
    }

    public function planValidate(Request $request)
    {
        abort_if(Gate::denies('product_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = Product::where('status', '2')->with(['product_category', 'product_tags', 'supplier', 'country', 'currency', 'team', 'store'])->select(sprintf('%s.*', (new Product)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'product_show';
                $editGate      = 'product_edit';
                $deleteGate    = 'product_delete';
                $crudRoutePart = 'products';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('product_code', function ($row) {
                return $row->product_code ? $row->product_code : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? '<a href="' . route('admin.products.show', $row->id) . '" class="badge bg-primary text-white">' . $row->name . '</a>' : '';
            });
            $table->addColumn('product_category_title', function ($row) {
                return $row->product_category ? $row->product_category->title : '';
            });

            $table->editColumn('product_tag', function ($row) {
                $labels = [];
                foreach ($row->product_tags as $product_tag) {
                    $labels[] = sprintf('<span class="label label-info label-many">%s</span>', $product_tag->title);
                }

                return implode(' ', $labels);
            });
            $table->editColumn('available_stock_level', function ($row) {
                return $row->available_stock_level ? $row->available_stock_level : '';
            });
            $table->addColumn('supplier_company_type', function ($row) {
                return $row->supplier ? $row->supplier->company_type : '';
            });

            $table->addColumn('country_name', function ($row) {
                return $row->country ? $row->country->name : '';
            });

            $table->addColumn('currency_name', function ($row) {
                return $row->currency ? $row->currency->name : '';
            });

            $table->editColumn('rate_of_exchange', function ($row) {
                return $row->rate_of_exchange ? $row->rate_of_exchange : '';
            });
            $table->editColumn('unit_buying_in_currency', function ($row) {
                return $row->unit_buying_in_currency ? $row->unit_buying_in_currency : '';
            });
            $table->editColumn('unit_buying_local_price', function ($row) {
                return $row->unit_buying_local_price ? $row->unit_buying_local_price : '';
            });
            $table->editColumn('charges', function ($row) {
                return $row->charges ? $row->charges : '';
            });
            $table->editColumn('shipping_charges', function ($row) {
                return $row->shipping_charges ? $row->shipping_charges : '';
            });
            $table->editColumn('clearence', function ($row) {
                return $row->clearence ? $row->clearence : '';
            });
            $table->editColumn('clearence_type', function ($row) {
                return $row->clearence_type ? Product::CLEARENCE_TYPE_RADIO[$row->clearence_type] : '';
            });
            $table->editColumn('duties', function ($row) {
                return $row->duties ? $row->duties : '';
            });
            $table->editColumn('duties_type', function ($row) {
                return $row->duties_type ? Product::DUTIES_TYPE_RADIO[$row->duties_type] : '';
            });
            $table->editColumn('delivery_charges', function ($row) {
                return $row->delivery_charges ? $row->delivery_charges : '';
            });
            $table->editColumn('net_price', function ($row) {
                return $row->net_price ? $row->net_price : '';
            });
            $table->editColumn('discount_rebate_refund', function ($row) {
                return $row->discount_rebate_refund ? $row->discount_rebate_refund : '';
            });
            $table->editColumn('discount_rebate_refund_type', function ($row) {
                return $row->discount_rebate_refund_type ? Product::DISCOUNT_REBATE_REFUND_TYPE_RADIO[$row->discount_rebate_refund_type] : '';
            });
            $table->editColumn('mark_up_price', function ($row) {
                return $row->mark_up_price ? $row->mark_up_price : '';
            });
            $table->editColumn('margin', function ($row) {
                return $row->margin ? $row->margin : '';
            });
            $table->editColumn('mark_up_price_type', function ($row) {
                return $row->mark_up_price_type ? Product::MARK_UP_PRICE_TYPE_RADIO[$row->mark_up_price_type] : '';
            });
            $table->editColumn('margin_type', function ($row) {
                return $row->margin_type ? Product::MARGIN_TYPE_RADIO[$row->margin_type] : '';
            });
            $table->editColumn('net_selling_price', function ($row) {
                return $row->net_selling_price ? $row->net_selling_price : '';
            });
            $table->editColumn('brand', function ($row) {
                return $row->brand ? $row->brand : '';
            });
            $table->addColumn('store_title', function ($row) {
                return $row->store ? $row->store->title : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'product_category', 'product_tag', 'supplier', 'country', 'currency', 'store','name']);

            return $table->make(true);
        }

        return view('admin.products.plan_validate');
    }

    public function create()
    {
        abort_if(Gate::denies('product_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $product_categories = ProductCategory::where('level', 1)->pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');
        $product_sub_categories = collect()->prepend(trans('global.pleaseSelect'), '');
        $product_sub_sub_categories = collect()->prepend(trans('global.pleaseSelect'), '');

        $product_tags = ProductTag::pluck('title', 'id');

        $suppliers = Supplier::pluck('company_type', 'id')->prepend(trans('global.pleaseSelect'), '');

        $countries = Country::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $currencies = Country::pluck('currency', 'id')->prepend(trans('global.pleaseSelect'), '');

        $stores = Store::pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.products.create', compact('countries', 'currencies', 'product_categories', 'product_sub_categories', 'product_sub_sub_categories', 'product_tags', 'stores', 'suppliers'));
    }

    public function store(StoreProductRequest $request)
    {

        if ($request->ajax()) {

            $product = Product::create($request->all());
            $product->product_tags()->sync($request->input('product_tags', []));

            return response()->json([
                'success' => 'Status updated successfully.',
                'product' => $product
            ]);

        }

        $product = Product::create($request->all());
        $product->product_tags()->sync($request->input('product_tags', []));

        if ($request->input('product_image', false)) {
            $product->addMedia(storage_path('tmp/uploads/' . basename($request->input('product_image'))))->toMediaCollection('product_image');
        }

        if ($media = $request->input('ck-media', false)) {
            Media::whereIn('id', $media)->update(['model_id' => $product->id]);
        }

        return redirect()->route('admin.products.edit', $product->id);
    }

    public function edit(Product $product)
    {
        abort_if(Gate::denies('product_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $product_categories = ProductCategory::where('level', 1)->pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        // Get sub categories based on selected main category
        $product_sub_categories = collect()->prepend(trans('global.pleaseSelect'), '');
        if ($product->product_category_id) {
            $product_sub_categories = ProductCategory::where('parent_id', $product->product_category_id)
                ->where('level', 2)
                ->pluck('title', 'id')
                ->prepend(trans('global.pleaseSelect'), '');
        }

        // Get sub sub categories based on selected sub category
        $product_sub_sub_categories = collect()->prepend(trans('global.pleaseSelect'), '');
        if ($product->product_sub_category_id) {
            $product_sub_sub_categories = ProductCategory::where('parent_id', $product->product_sub_category_id)
                ->where('level', 3)
                ->pluck('title', 'id')
                ->prepend(trans('global.pleaseSelect'), '');
        }

        $product_tags = ProductTag::pluck('title', 'id');

        $suppliers = Supplier::pluck('company_type', 'id')->prepend(trans('global.pleaseSelect'), '');

        $countries = Country::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $currencies = Country::pluck('currency', 'id')->prepend(trans('global.pleaseSelect'), '');

        $stores = Store::pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        $projects = Project::pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        $product->load('product_category', 'product_sub_category', 'product_sub_sub_category', 'product_tags', 'supplier', 'country', 'currency', 'team', 'store');

        return view('admin.products.edit', compact('countries', 'currencies', 'product','projects', 'product_categories', 'product_sub_categories', 'product_sub_sub_categories', 'product_tags', 'stores', 'suppliers'));
    }

    public function update(UpdateProductRequest $request, Product $product)
    {
        $product->update($request->all());
        $product->product_tags()->sync($request->input('product_tags', []));

        if ($request->input('product_image', false)) {
            if (! $product->product_image || $request->input('product_image') !== $product->product_image->file_name) {
                if ($product->product_image) {
                    $product->product_image->delete();
                }
                $product->addMedia(storage_path('tmp/uploads/' . basename($request->input('product_image'))))->toMediaCollection('product_image');
            }
        } elseif ($product->product_image) {
            $product->product_image->delete();
        }

        return redirect()->route('admin.products.index');
    }

    public function show(Product $product)
    {
        abort_if(Gate::denies('product_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $product->load('product_category', 'product_tags', 'supplier', 'country', 'currency', 'team', 'store', 'productCodeInvoiceItems', 'productCodeQuotationItems');

        return view('admin.products.show', compact('product'));
    }

    public function destroy(Product $product)
    {
        abort_if(Gate::denies('product_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $product->delete();

        return back();
    }

    public function massDestroy(MassDestroyProductRequest $request)
    {
        $products = Product::find(request('ids'));

        foreach ($products as $product) {
            $product->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function storeCKEditorImages(Request $request)
    {
        abort_if(Gate::denies('product_create') && Gate::denies('product_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $model         = new Product();
        $model->id     = $request->input('crud_id', 0);
        $model->exists = true;
        $media         = $model->addMediaFromRequest('upload')->toMediaCollection('ck-media');

        return response()->json(['id' => $media->id, 'url' => $media->getUrl()], Response::HTTP_CREATED);
    }


    public function fetchItemCodes(Request $request)
    {
        try {
            $itemCodes = Product::orderBy('created_at', 'desc')->take(4)->get();
            return response()->json($itemCodes);
        } catch (\Exception $e) {
            \Log::error('Error fetching item codes: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch item codez'], 500);
        }
    }

    public function searchItemCodes(Request $request)
    {
        try {

            $itemCodes = Product::where('product_code', 'like', '%' . $request->search . '%')
            ->orWhere('name', 'like', '%' . $request->search . '%')->orderBy('created_at', 'desc')->take(4)->get();

            return response()->json($itemCodes);
        } catch (\Exception $e) {
            \Log::error('Error fetching item codes: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch item codez'], 500);
        }
    }

    // itemDetail
    public function itemDetail(Request $request)
    {
        try {
            $itemDetail = Product::where('id', $request->id)->first();

            if(auth()->user()->team->enable_margin) {
                $cost_price = $this->getCostPriceFromList($itemDetail->product_code);
                $itemDetail->cost_price = $cost_price;
            }

            return response()->json($itemDetail);
        } catch (\Exception $e) {
            \Log::error('Error fetching item detail: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch item detail'], 500);
        }
    }

    public function getCostPriceFromList($item_code)
    {
        try {

            // Get the price list
            $priceList = PriceList::latest()->first();
            if (!$priceList) {
                return 0;
            }

            $priceListFile = $priceList->price_list;
            if (!$priceListFile) {
                return 0;
            }
            $priceListPath = $priceListFile->getPath();

            if (!$priceListPath) {
                return 0;
            }

            if(!file_exists($priceListPath)) {
                return 0;
            }

            if(!is_readable($priceListPath)) {
                return 0;
            }

            $priceListContent = file_get_contents($priceListPath);
            $priceListData = str_getcsv($priceListContent, "\n");
            $priceListData = array_map(function($row) {
                return str_getcsv($row, ",");
            }, $priceListData);

            foreach ($priceListData as $row) {
                if ($row[0] == $item_code) {
                    return number_format(floatval($row[1]), 2) ?? 0;
                }
            }

        }
        catch (\Exception $e) {
            return 0;
        }
    }


}
