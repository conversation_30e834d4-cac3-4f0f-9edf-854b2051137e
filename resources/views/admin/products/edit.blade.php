@extends('layouts.heyadmin')
@section('title')
    {{ trans('global.edit') }} {{ trans('cruds.product.title_singular') }}
@endsection
@section('styles')
    <style>
        .discount-toggle-container {
            text-align: center;
            /* Center align the content */
            display: inline-block;
            /* Inline-block to ensure the elements stack */
            padding: 0;
            margin: 0;
        }


        .toggle-wrapper {
            margin: -3px auto;
            padding-left: 13%;
            /* Center horizontally */
        }

        .toggleContainer {
            position: relative;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            width: 56px;
            border: .5px solid #343434;
            border-radius: 10px;
            background: #5c2d91;
            font-weight: bold;
            font-size: 0.7rem;
            color: #343434;
            cursor: pointer;
            margin-bottom: 4px;
        }

        .toggleContainer::before {
            content: '';
            position: absolute;
            width: 50%;
            height: 100%;
            left: 0%;
            border-radius: 10px;
            background: #fff;
            transition: all 0.3s;
        }

        .toggleCheckbox:checked+.toggleContainer::before {
            left: 50%;
        }

        .toggleContainer div {
            padding: 1px 3px;
            text-align: center;
            z-index: 1;
        }

        .toggleCheckbox {
            display: none;
        }

        .toggleCheckbox:checked+.toggleContainer div:first-child {
            color: white;
            transition: color 0.3s;
        }

        .toggleCheckbox:checked+.toggleContainer div:last-child {
            color: #343434;
            transition: color 0.3s;
        }

        .toggleCheckbox+.toggleContainer div:first-child {
            color: #343434;
            transition: color 0.3s;
        }

        .toggleCheckbox+.toggleContainer div:last-child {
            color: white;
            transition: color 0.3s;
        }
    </style>
@endsection
@section('title')
    {{ trans('cruds.product.title_singular') }}
@endsection

@section('toolbar')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.product.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ route('admin.products.index') }}" class="text-muted text-hover-primary">{{ trans('cruds.product.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.edit') }}
                        {{ trans('cruds.product.title_singular') }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <form method="POST" action="{{ route('admin.products.update', [$product->id]) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="row g-0">

                        <div class="col-lg-12 col-12 pe-lg-2">
                            <div class="card mb-5 p-4 p-sm-5">
                                <div class="card-head">
                                    <div class="row">
                                        <div class="col-4">
                                            <h2>{{ trans('cruds.product.title_singular') }} - {{ trans('global.edit') }}</h2>
                                        </div>
                                        {{-- <div class="col-6">
                                            <button type="submit" class="btn btn-sm btn-light-primary">Purchase</button>
                                            <button type="submit" class="btn btn-sm btn-light-primary">Sales</button>
                                            <button type="submit" class="btn btn-sm btn-light-primary">Rental</button>
                                        </div> --}}
                                        <div class="col-8 text-end">
                                            <a href="{{ route('admin.products.index') }}" class="btn btn-sm btn-primary">{{ trans('global.back') }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-0">

                            <div class="col-6 pe-2">
                                <div class="row g-0">

                                    <div class="col-12">
                                        <div class="col-lg-12 col-12 pe-lg-2">
                                            <div class="card mb-2 p-4 p-sm-5">
                                                <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                                    <h4>{{ trans('cruds.product.title_singular') }} Details</h4>
                                                </div>
                                                <div class="card-body p-0">
                                                    <div class="row">

                                                        <div class="col-12">
                                                            <div class="form-group mb-3">
                                                                <label class="form-label me-6">{{ trans('cruds.product.fields.vat_strategy') }}</label>
                                                                @foreach (App\Models\Product::VAT_STRATEGY_RADIO as $key => $label)
                                                                    <div class="form-check form-check-inline {{ $errors->has('vat_strategy') ? 'is-invalid' : '' }}">
                                                                        <input class="form-check-input" type="radio" id="vat_strategy_{{ $key }}" name="vat_strategy" value="{{ $key }}" {{ old('vat_strategy', $product->vat_strategy) === (string) $key ? 'checked' : '' }}>
                                                                        <label class="form-check-label" for="vat_strategy_{{ $key }}">{{ $label }}</label>
                                                                    </div>
                                                                @endforeach
                                                                @if ($errors->has('vat_strategy'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('vat_strategy') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.vat_strategy_helper') }}</span>
                                                            </div>
                                                        </div>

                                                        <div class="col-12">
                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <div class="form-group mb-3 h-80" style="height: 80%;">
                                                                        <label class="form-label" for="product_image">Product Image</label>
                                                                        <div class="h-100 needsclick dropzone {{ $errors->has('product_image') ? 'is-invalid' : '' }}" id="product_image-dropzone"></div>
                                                                        @if ($errors->has('product_image'))
                                                                            <div class="invalid-feedback">
                                                                                {{ $errors->first('product_image') }}
                                                                            </div>
                                                                        @endif
                                                                        <span class="help-block">{{ trans('cruds.product.fields.product_image_helper') }}</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-6">
                                                                    <div class="form-group mb-3">
                                                                        <label class="form-label required" for="name">{{ trans('cruds.product.fields.name') }}</label>
                                                                        <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $product->name) }}" required>
                                                                        @if ($errors->has('name'))
                                                                            <div class="invalid-feedback">
                                                                                {{ $errors->first('name') }}
                                                                            </div>
                                                                        @endif
                                                                        <span class="help-block">{{ trans('cruds.product.fields.name_helper') }}</span>
                                                                    </div>
                                                                    <div class="form-group mb-3">
                                                                        <label class="form-label required" for="unit_sales_price">{{ trans('cruds.product.fields.unit_sales_price') }}</label>
                                                                        <input class="form-control form-control-sm {{ $errors->has('unit_sales_price') ? 'is-invalid' : '' }}" type="text" name="unit_sales_price" id="unit_sales_price" value="{{ old('name', $product->unit_sales_price) }}" required>
                                                                        @if ($errors->has('unit_sales_price'))
                                                                            <div class="invalid-feedback">
                                                                                {{ $errors->first('unit_sales_price') }}
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                    <div class="form-group mb-3">
                                                                        <label class="form-label required" for="product_code">{{ trans('cruds.product.fields.product_code') }}</label>
                                                                        <input class="form-control form-control-sm {{ $errors->has('product_code') ? 'is-invalid' : '' }}" type="text" name="product_code" id="product_code" value="{{ old('product_code', $product->product_code) }}" required>
                                                                        @if ($errors->has('product_code'))
                                                                            <div class="invalid-feedback">
                                                                                {{ $errors->first('product_code') }}
                                                                            </div>
                                                                        @endif
                                                                        <span class="help-block">{{ trans('cruds.product.fields.product_code_helper') }}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-12">
                                                                    <div class="form-group mb-3">
                                                                        <label class="form-label" for="description">{{ trans('cruds.product.fields.description') }}</label>
                                                                        <textarea class="form-control {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description" id="description" style="height: 150px">{{ old('description', $product->description) }}</textarea>
                                                                        @if ($errors->has('description'))
                                                                            <div class="invalid-feedback">
                                                                                {{ $errors->first('description') }}
                                                                            </div>
                                                                        @endif
                                                                        <span class="help-block">{{ trans('cruds.product.fields.description_helper') }}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-12">

                                                            <div class="form-group mb-3">
                                                                <label class="form-label" for="brand">{{ trans('cruds.product.fields.brand') }}</label>
                                                                <input class="form-control form-control-sm {{ $errors->has('brand') ? 'is-invalid' : '' }}" type="text" name="brand" id="brand" value="{{ old('brand', $product->brand) }}">
                                                                @if ($errors->has('brand'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('brand') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.brand_helper') }}</span>
                                                            </div>

                                                            <div class="form-group mb-3">
                                                                <label class="form-label" for="product_category_id">{{ trans('cruds.product.fields.product_category') }}</label>
                                                                <div class="input-group input-group-solid flex-nowrap">
                                                                    <div class="overflow-hidden flex-grow-1">
                                                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('product_category') ? 'is-invalid' : '' }}" name="product_category_id" id="product_category_id" data-control="select2">
                                                                            @foreach ($product_categories as $id => $entry)
                                                                            <option value="{{ $id }}" {{ $product->product_category_id == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <span class="btn btn-sm btn-primary" id="open_modal_product_category"> <i class="fa-solid fa-plus"></i></span>
                                                                </div>
                                                                @if ($errors->has('product_category'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('product_category') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.product_category_helper') }}</span>
                                                                <label id="product_category_id-error" class="error" for="product_category_id" style="display: none">This field is required.</label>
                                                            </div>

                                                            <div class="form-group mb-3">
                                                                <label class="form-label" for="product_sub_category_id">Sub Category</label>
                                                                <div class="input-group input-group-solid flex-nowrap">
                                                                    <div class="overflow-hidden flex-grow-1">
                                                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('product_sub_category') ? 'is-invalid' : '' }}" name="product_sub_category_id" id="product_sub_category_id" data-control="select2">
                                                                            @foreach ($product_sub_categories as $id => $entry)
                                                                                <option value="{{ $id }}" {{ $product->product_sub_category_id == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <span class="btn btn-sm btn-primary" id="open_modal_product_sub_category"> <i class="fa-solid fa-plus"></i></span>
                                                                </div>
                                                                @if ($errors->has('product_sub_category'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('product_sub_category') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">Sub category for the product</span>
                                                                <label id="product_sub_category_id-error" class="error" for="product_sub_category_id" style="display: none">This field is required.</label>
                                                            </div>

                                                            <div class="form-group mb-3">
                                                                <label class="form-label" for="product_sub_sub_category_id">Sub Sub Category</label>
                                                                <div class="input-group input-group-solid flex-nowrap">
                                                                    <div class="overflow-hidden flex-grow-1">
                                                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('product_sub_sub_category') ? 'is-invalid' : '' }}" name="product_sub_sub_category_id" id="product_sub_sub_category_id" data-control="select2">
                                                                            @foreach ($product_sub_sub_categories as $id => $entry)
                                                                                <option value="{{ $id }}" {{ $product->product_sub_sub_category_id == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <span class="btn btn-sm btn-primary" id="open_modal_product_sub_sub_category"> <i class="fa-solid fa-plus"></i></span>
                                                                </div>
                                                                @if ($errors->has('product_sub_sub_category'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('product_sub_sub_category') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">Sub sub category for the product</span>
                                                                <label id="product_sub_sub_category_id-error" class="error" for="product_sub_sub_category_id" style="display: none">This field is required.</label>
                                                            </div>

                                                            <div class="form-group mb-3">
                                                                <label class="form-label" for="product_tags">{{ trans('cruds.product.fields.product_tag') }}</label>
                                                                <div class="input-group input-group-solid flex-nowrap">
                                                                    <div class="overflow-hidden flex-grow-1">
                                                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('product_tags') ? 'is-invalid' : '' }}" name="product_tags[]" id="product_tags_id" data-control="select2" multiple="multiple">
                                                                            @foreach ($product_tags as $id => $entry)
                                                                                <option value="{{ $id }}" {{ in_array($id, old('product_tags', [])) || $product->product_tags->contains($id) ? 'selected' : '' }}>{{ $entry }}</option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <span class="btn btn-sm btn-primary" id="open_modal_product_tags"> <i class="fa-solid fa-plus"></i></span>
                                                                </div>
                                                                @if ($errors->has('product_tags'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('product_tags') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.product_tag_helper') }}</span>
                                                                <label id="product_tags-error" class="error" for="product_tags" style="display: none">This field is required.</label>
                                                            </div>

                                                        </div>
                                                        <div class="col-12">

                                                            <div class="form-group mb-3">
                                                                <label class="form-label">Unit Of Sales</label>
                                                                <select class="form-select form-select-sm " name="unit_of_sales" data-control="select2" id="unit_of_sales">
                                                                    <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
                                                                    @foreach (App\Models\Product::UNIT_OF_SALES as $key => $label)
                                                                    <option value="{{ $key }}" {{ old('unit_of_sales', $product->unit_of_sales) === (string) $key ? 'selected' : '' }}>{{ $label }}</option>
                                                                    @endforeach
                                                                </select>
                                                            </div>

                                                            <div class="form-group mb-2">
                                                                <label class="form-label" for="store_id">{{ trans('cruds.product.fields.store') }}</label>
                                                                <select class="form-select form-select-sm {{ $errors->has('store') ? 'is-invalid' : '' }}" name="store_id" id="store_id" data-control="select2">
                                                                    @foreach ($stores as $id => $entry)
                                                                        <option value="{{ $id }}" {{ $product->store_id == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                                                    @endforeach
                                                                </select>
                                                                @if ($errors->has('store'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('store') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.store_helper') }}</span>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-12">
                                            {{-- Supplier Link Start --}}
                                            <div class="col-lg-12 col-12 pe-lg-2">
                                                <div class="card mb-2 p-4 p-sm-5">
                                                    <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                                        <h4>{{ trans('cruds.product.fields.link') }} {{ trans('cruds.product.fields.supplier') }}</h4>
                                                        <div class="discount-toggle-container">
                                                            <div class="toggle-wrapper">
                                                                <input type="checkbox" {{ $product->toggle_supplier_item == '1'  ? 'checked' : '' }}  id="toggle_supplier_item" class="toggleCheckbox" name="toggle_supplier_item" value="1" />
                                                                <label for="toggle_supplier_item" class="toggleContainer">
                                                                    <div>No</div>
                                                                    <div>Yes</div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="card-body p-0 supplier-item">
                                                        <div class="row">

                                                            <div class="form-group mb-3 col-12">
                                                                <label class="form-label" for="supplier_id">{{ trans('cruds.product.fields.supplier') }}</label>
                                                                <div class="input-group input-group-solid flex-nowrap">
                                                                    <div class="overflow-hidden flex-grow-1">
                                                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('supplier') ? 'is-invalid' : '' }}" name="supplier_id" id="supplier_id" data-control="select2">
                                                                            @foreach ($suppliers as $id => $entry)
                                                                                <option value="{{ $id }}" {{ (old('supplier_id') ? old('supplier_id') : $product->supplier->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                    <span class="btn btn-sm btn-primary" id="open_modal_supplier"> <i class="fa-solid fa-plus"></i></span>
                                                                </div>
                                                                @if ($errors->has('supplier'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('supplier') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.supplier_helper') }}</span>
                                                                <label id="supplier_id-error" class="error" for="supplier_id" style="display: none">This field is required.</label>
                                                            </div>



                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {{-- Supplier Link End --}}
                                        </div>

                                        <div class="col-12">
                                            <div class="col-lg-12 col-12 pe-lg-2">
                                                <div class="card mb-5 p-4 p-sm-5">

                                                    <div class="card-body p-0">
                                                        <div class="row">

                                                            <div class="form-group mb-3 col-12">
                                                                <label class="form-label" for="comments">{{ trans('cruds.product.fields.comments') }}</label>
                                                                <textarea class="form-control {{ $errors->has('comments') ? 'is-invalid' : '' }}" name="comments" id="comments" style="height: 150px">{{ old('comments', $product->comments ) }}</textarea>
                                                                @if ($errors->has('comments'))
                                                                    <div class="invalid-feedback">
                                                                        {{ $errors->first('comments') }}
                                                                    </div>
                                                                @endif
                                                                <span class="help-block">{{ trans('cruds.product.fields.comments_helper') }}</span>
                                                            </div>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                </div>
                            </div>

                            <div class="col-6">


                                {{-- Costing Start --}}
                                <div class="col-lg-12 col-12 pe-lg-2">
                                    <div class="card mb-2 p-4 p-sm-5">
                                        <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                            <h4>{{ trans('cruds.product.fields.calculate') }} {{ trans('cruds.product.fields.costing') }} ?</h4>

                                            <div class="discount-toggle-container">
                                                <div class="toggle-wrapper">
                                                    <input type="checkbox" {{ $product->toggle_costing_item == '1'  ? 'checked' : '' }} id="toggle_costing_item" class="toggleCheckbox" name="toggle_costing_item" value="1" />
                                                    <label for="toggle_costing_item" class="toggleContainer">
                                                        <div>No</div>
                                                        <div>Yes</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card-body p-0 costing-item ">
                                            <div class="row">
                                                <div class="form-group mb-3 col">
                                                    <div class="form-check form-switch form-check-custom form-check-solid">
                                                        <div class="discount-toggle-container">
                                                            <div class="toggle-wrapper ps-0">
                                                                <input type="checkbox" {{ $product->apply_rate_of_exchange == '1'  ? 'checked' : '' }}  id="checkbox_rate_of_exchange" class="toggleCheckbox" name="apply_rate_of_exchange" value="1" />
                                                                <label for="checkbox_rate_of_exchange" class="toggleContainer">
                                                                    <div>No</div>
                                                                    <div>Yes</div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <label class="form-check-label ms-3 pb-2" for="flexSwitchDefault">
                                                            Apply Rate Of Exchange
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="form-group mb-3 col-6">
                                                    <label class="form-label" for="currency_id">{{ trans('cruds.product.fields.currency') }}</label>
                                                    <select class="form-select form-select-sm {{ $errors->has('currency') ? 'is-invalid' : '' }}" name="currency_id" id="currency_id" data-control="select2">
                                                        @foreach ($currencies as $id => $entry)
                                                            <option value="{{ $id }}" {{ $product->currency_id == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                                        @endforeach
                                                    </select>
                                                    @if ($errors->has('currency'))
                                                        <div class="invalid-feedback">
                                                            {{ $errors->first('currency') }}
                                                        </div>
                                                    @endif
                                                    <span class="help-block">{{ trans('cruds.product.fields.currency_helper') }}</span>
                                                </div>
                                            </div>

                                            <div class="row fade-in" id="rate_of_exchange_div">
                                                <div class="form-group mb-3 col-6">
                                                    <label class="form-label" for="unit_buying_in_currency">
                                                        {{ trans('cruds.product.fields.unit_buying_in_currency') }}
                                                    </label>
                                                    <input class="form-control form-control-sm {{ $errors->has('unit_buying_in_currency') ? 'is-invalid' : '' }}" type="number" name="unit_buying_in_currency" id="unit_buying_in_currency" value="{{ old('unit_buying_in_currency', $product->unit_buying_in_currency) }}" step="0.01">
                                                    @if ($errors->has('unit_buying_in_currency'))
                                                        <div class="invalid-feedback">
                                                            {{ $errors->first('unit_buying_in_currency') }}
                                                        </div>
                                                    @endif
                                                    <span class="help-block">{{ trans('cruds.product.fields.unit_buying_in_currency_helper') }}</span>
                                                </div>

                                                <div class="form-group mb-3 col-6">
                                                    <label class="form-label" for="rate_of_exchange">
                                                        {{ trans('cruds.product.fields.rate_of_exchange') }}
                                                    </label>
                                                    <input class="form-control form-control-sm {{ $errors->has('rate_of_exchange') ? 'is-invalid' : '' }}" type="number" name="rate_of_exchange" id="rate_of_exchange" value="{{ old('rate_of_exchange', $product->rate_of_exchange) }}" step="0.01">
                                                    @if ($errors->has('rate_of_exchange'))
                                                        <div class="invalid-feedback">
                                                            {{ $errors->first('rate_of_exchange') }}
                                                        </div>
                                                    @endif
                                                    <span class="help-block">{{ trans('cruds.product.fields.rate_of_exchange_helper') }}</span>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="form-group mb-3 col-6">
                                                    <label class="form-label" for="unit_buying_local_price">
                                                        {{ trans('cruds.product.fields.unit_buying_local_price') }}
                                                    </label>
                                                    <input class="form-control-solid form-control form-control-sm {{ $errors->has('unit_buying_local_price') ? 'is-invalid' : '' }}" type="number" name="unit_buying_local_price" id="unit_buying_local_price" value="{{ old('unit_buying_local_price', $product->unit_buying_local_price) }}" step="0.01" readonly>
                                                    @if ($errors->has('unit_buying_local_price'))
                                                        <div class="invalid-feedback">
                                                            {{ $errors->first('unit_buying_local_price') }}
                                                        </div>
                                                    @endif
                                                    <span class="help-block">{{ trans('cruds.product.fields.unit_buying_local_price_helper') }}</span>
                                                </div>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="charges">
                                                    {{ trans('cruds.product.fields.charges') }}
                                                </label>
                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->charges_type == 'num'  ? 'checked' : '' }} type="checkbox" id="charges_toggle" class="toggleCheckbox" name="charges_toggle" />
                                                        <label for="charges_toggle" class="toggleContainer">
                                                            <input type="hidden" id="charges_type" name="charges_type" value="{{$product->charges_type}}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>

                                                <input class="form-control form-control-sm {{ $errors->has('charges') ? 'is-invalid' : '' }}" type="text" name="charges" id="charges" value="{{ old('charges', $product->charges) }}">
                                                @if ($errors->has('charges'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('charges') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.charges_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="shipping_charges">
                                                    {{ trans('cruds.product.fields.shipping_charges') }}
                                                </label>
                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->shipping_charges_type == 'num'  ? 'checked' : '' }} type="checkbox" id="shipping_charges_toggle" class="toggleCheckbox" name="shipping_charges_toggle"  />
                                                        <label for="shipping_charges_toggle" class="toggleContainer">
                                                            <input type="hidden" id="shipping_charges_type" name="shipping_charges_type" value="{{$product->shipping_charges_type}}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>

                                                <input class="form-control form-control-sm {{ $errors->has('shipping_charges') ? 'is-invalid' : '' }}" type="text" name="shipping_charges" id="shipping_charges" value="{{ old('shipping_charges', $product->shipping_charges) }}">
                                                @if ($errors->has('shipping_charges'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('shipping_charges') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.shipping_charges_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="clearence">
                                                    {{ trans('cruds.product.fields.clearence') }}
                                                </label>
                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->clearence_type == 'num'  ? 'checked' : '' }} type="checkbox" id="clearence_type_toggle" class="toggleCheckbox" name="clearence_type_toggle" />
                                                        <label for="clearence_type_toggle" class="toggleContainer">
                                                            <input type="hidden" id="clearence_type" name="clearence_type" value="{{ $product->clearence_type }}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>

                                                <input class="form-control form-control-sm {{ $errors->has('clearence') ? 'is-invalid' : '' }}" type="text" name="clearence" id="clearence" value="{{ old('clearence', $product->clearence) }}">
                                                @if ($errors->has('clearence'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('clearence') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.clearence_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="duties">
                                                    {{ trans('cruds.product.fields.duties') }}
                                                </label>
                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->duties_type_toggle == 'num'  ? 'checked' : '' }} type="checkbox" id="duties_type_toggle" class="toggleCheckbox" name="duties_type_toggle" />
                                                        <label for="duties_type_toggle" class="toggleContainer">
                                                            <input type="hidden" id="duties_type" name="duties_type" value="{{ $product->duties_type }}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>
                                                <input class="form-control form-control-sm {{ $errors->has('duties') ? 'is-invalid' : '' }}" type="text" name="duties" id="duties" value="{{ old('duties', $product->duties) }}">
                                                @if ($errors->has('duties'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('duties') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.duties_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="delivery_charges">
                                                    {{ trans('cruds.product.fields.delivery_charges') }}
                                                </label>
                                                <input class="form-control form-control-sm {{ $errors->has('delivery_charges') ? 'is-invalid' : '' }}" type="text" name="delivery_charges" id="delivery_charges" value="{{ old('delivery_charges', $product->delivery_charges) }}">
                                                @if ($errors->has('delivery_charges'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('delivery_charges') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.delivery_charges_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="net_price">
                                                    {{ trans('cruds.product.fields.net_price') }}
                                                </label>
                                                <input class="form-control-solid form-control  form-control-sm {{ $errors->has('net_price') ? 'is-invalid' : '' }}" type="text" name="net_price" id="net_price" value="{{ old('net_price', $product->net_price) }}" readonly>
                                                @if ($errors->has('net_price'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('net_price') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.net_price_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="discount_rebate_refund">
                                                    {{ trans('cruds.product.fields.discount_rebate_refund') }}
                                                </label>
                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->discount_rebate_refund_type_toggle == 'num'  ? 'checked' : '' }} type="checkbox" id="discount_rebate_refund_type_toggle" class="toggleCheckbox" name="discount_rebate_refund_type_toggle" />
                                                        <label for="discount_rebate_refund_type_toggle" class="toggleContainer">
                                                            <input type="hidden" id="discount_rebate_refund_type" name="discount_rebate_refund_type" value="{{ $product->discount_rebate_refund_type }}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>
                                                <input class="form-control form-control-sm {{ $errors->has('discount_rebate_refund') ? 'is-invalid' : '' }}" type="text" name="discount_rebate_refund" id="discount_rebate_refund" value="{{ old('discount_rebate_refund', $product->discount_rebate_refund) }}">
                                                @if ($errors->has('discount_rebate_refund'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('discount_rebate_refund') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.discount_rebate_refund_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="new_sale_price">
                                                    New Sale Price
                                                </label>
                                                <input class="form-control form-control-sm form-control-solid" type="text" name="new_sale_price" id="new_sale_price" value="{{ $product->new_sale_price }}" readonly />
                                            </div>

                                            <h4 class="my-2">Pricing</h4>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="margin">{{ trans('cruds.product.fields.margin') }}</label>

                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->margin_type_toggle == 'num'  ? 'checked' : '' }} type="checkbox" id="margin_type_toggle" class="toggleCheckbox" name="margin_type_toggle" />
                                                        <label for="margin_type_toggle" class="toggleContainer">
                                                            <input type="hidden" id="margin_type" name="margin_type" value="{{ $product->margin_type }}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>

                                                <input class="form-control form-control-sm {{ $errors->has('margin') ? 'is-invalid' : '' }}" type="text" name="margin" id="margin" value="{{ old('margin', $product->margin) }}">
                                                @if ($errors->has('margin'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('margin') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.margin_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="mark_up_price">{{ trans('cruds.product.fields.mark_up_price') }}</label>

                                                <div class="discount-toggle-container">
                                                    <div class="toggle-wrapper">
                                                        <input {{ $product->mark_up_price_toggle == 'num'  ? 'checked' : '' }}  type="checkbox" id="mark_up_price_toggle" class="toggleCheckbox" name="mark_up_price_toggle" />
                                                        <label for="mark_up_price_toggle" class="toggleContainer">
                                                            <input type="hidden" id="mark_up_price_type" name="mark_up_price_type" value="{{ $product->mark_up_price_toggle }}" />
                                                            <div>%</div>
                                                            <div>Num</div>
                                                        </label>
                                                    </div>
                                                </div>
                                                <input class="form-control form-control-sm {{ $errors->has('mark_up_price') ? 'is-invalid' : '' }}" type="text" name="mark_up_price" id="mark_up_price" value="{{ old('mark_up_price', $product->mark_up_price) }}">
                                                @if ($errors->has('mark_up_price'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('mark_up_price') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.product.fields.mark_up_price_helper') }}</span>
                                            </div>

                                            <div class="form-group mb-3 col-12">
                                                <label class="form-label" for="new_sale_price">
                                                    Final Sale Price
                                                </label>
                                                <input class="form-control form-control-sm form-control-solid" type="text" name="final_sale_price" id="final_sale_price" value="{{ $product->final_sale_price  }}" readonly />
                                            </div>


                                        </div>
                                    </div>
                                </div>
                                {{-- Costing End --}}



                                {{-- Stock Management Start --}}
                                <div class="col-lg-12 col-12 pe-lg-2">
                                    <div class="card mb-2 p-4 p-sm-5">
                                        <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                            <h4>Stock Management</h4>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="row">

                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label class="form-label" for="opening_stock_level">{{ trans('cruds.product.fields.opening_stock_level') }}</label>
                                                            <input class="form-control form-control-sm {{ $errors->has('opening_stock_level') ? 'is-invalid' : '' }}" type="text" name="opening_stock_level" id="opening_stock_level" value="{{ old('opening_stock_level', $product->opening_stock_level) }}">
                                                            @if ($errors->has('opening_stock_level'))
                                                                <div class="invalid-feedback">
                                                                    {{ $errors->first('opening_stock_level') }}
                                                                </div>
                                                            @endif
                                                            <span class="help-block">{{ trans('cruds.product.fields.opening_stock_level_helper') }}</span>
                                                        </div>

                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label class="form-label" for="min_restock_level">New Quantity</label>
                                                            <input class="form-control form-control-sm {{ $errors->has('min_restock_level') ? 'is-invalid' : '' }}" type="text" name="min_restock_level" id="min_restock_level" value="{{ old('min_restock_level', $product->min_restock_level) }}">
                                                            @if ($errors->has('min_restock_level'))
                                                                <div class="invalid-feedback">
                                                                    {{ $errors->first('min_restock_level') }}
                                                                </div>
                                                            @endif
                                                            <span class="help-block">{{ trans('cruds.product.fields.min_restock_level_helper') }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label class="form-label" for="available_stock_level">Available Total Stock Level</label>
                                                            <input class="form-control form-control-sm {{ $errors->has('available_stock_level') ? 'is-invalid' : '' }}" type="text" name="available_stock_level" id="available_stock_level" value="{{ old('available_stock_level', $product->available_stock_level) }}">
                                                            @if ($errors->has('available_stock_level'))
                                                                <div class="invalid-feedback">
                                                                    {{ $errors->first('available_stock_level') }}
                                                                </div>
                                                            @endif
                                                            <span class="help-block">{{ trans('cruds.product.fields.min_restock_level_helper') }}</span>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label class="form-label" for="average_cost">Average Cost</label>
                                                            <input class="form-control form-control-sm {{ $errors->has('min_restock_level') ? 'is-invalid' : '' }}" type="text" name="average_cost" id="average_cost" value="{{ old('average_cost', $product->average_cost) }}">
                                                            @if ($errors->has('average_cost'))
                                                                <div class="invalid-feedback">
                                                                    {{ $errors->first('average_cost') }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <label>Re Order</label>
                                                        <div class="discount-toggle-container">
                                                            <div class="toggle-wrapper">
                                                                <input {{ $product->toggle_reorder == 'num'  ? 'checked' : '' }}  type="checkbox" id="toggle_reorder" class="toggleCheckbox" name="toggle_reorder" value="1" />
                                                                <label for="toggle_reorder" class="toggleContainer">
                                                                    <div>No</div>
                                                                    <div>Yes</div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {{-- Stock Management End --}}


                            </div>

                        </div>

                        <div class="col-lg-12 col-12 pe-lg-2">
                            <div class="card mb-2 p-4 p-sm-5">
                                <div class="card-body p-0">
                                    <div class="row">
                                        <div class="col-6 text-end">
                                            <a href="{{ route('admin.products.index') }}" class="btn btn-sm btn-light-secondary">{{ trans('global.back') }}</a>
                                        </div>
                                        <div class="col-6">
                                            <button type="submit" class="btn btn-sm btn-primary" id="customer_save">Continue</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal : Add Supplier -->
    <div class="modal fade" id="add_supplier_modal" tabindex="-1" aria-labelledby="add_supplier_modal" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add_supplier_title">Add Supplier</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add_supplier_modal_form" method="POST" action="{{ route('admin.suppliers.store') }}" enctype="multipart/form-data">
                        @csrf
                        @include('admin.suppliers.createForm')
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary add_supplier_modal_button">Add Supplier</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal : Add Product Tags -->
    <div class="modal fade" id="add_product_tags_modal" tabindex="-1" aria-labelledby="add_product_tags_modal" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add_product_tags_title">Add Product Tags</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add_product_tags_modal_form" method="POST" action="{{ route('admin.product-tags.store') }}" enctype="multipart/form-data">
                        @csrf
                        @include('admin.productTags.createForm')
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary add_product_tags_modal_button">Add Product Tags</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal : Add Product Category -->
    <div class="modal fade" id="modal_product_category" tabindex="-1" aria-labelledby="modal_product_category" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add_product_category_title">Add Product Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="modal_product_category_form" method="POST" action="{{ route('admin.product-categories.store') }}" enctype="multipart/form-data">
                        @csrf
                        @include('admin.productCategories.createForm')
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary add_product_category_modal_button">Add Product Category</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal : Add Product Sub Category -->
    <div class="modal fade" id="modal_product_sub_category" tabindex="-1" aria-labelledby="modal_product_sub_category" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add_product_sub_category_title">Add Sub Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="modal_product_sub_category_form" method="POST" action="{{ route('admin.product-categories.store') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group mb-3">
                            <label class="form-label required" for="sub_category_parent_id">Product Category</label>
                            <select class="form-select form-select-sm" name="parent_id" id="sub_category_parent_id" data-control="select2" required>
                                <option value="">{{ trans('global.pleaseSelect') }}</option>
                                @foreach ($product_categories as $id => $entry)
                                    <option value="{{ $id }}">{{ $entry }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label required" for="sub_category_title">Sub Category Title</label>
                            <input class="form-control form-control-sm" type="text" name="title" id="sub_category_title" required>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select form-select-sm" name="status" data-control="select2">
                                <option value="active" selected>Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary add_product_sub_category_modal_button">Add Sub Category</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal : Add Product Sub Sub Category -->
    <div class="modal fade" id="modal_product_sub_sub_category" tabindex="-1" aria-labelledby="modal_product_sub_sub_category" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add_product_sub_sub_category_title">Add Sub Sub Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="modal_product_sub_sub_category_form" method="POST" action="{{ route('admin.product-categories.store') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group mb-3">
                            <label class="form-label required" for="sub_sub_category_main_parent_id">Product Category</label>
                            <select class="form-select form-select-sm" name="main_parent_id" id="sub_sub_category_main_parent_id" data-control="select2" required>
                                <option value="">{{ trans('global.pleaseSelect') }}</option>
                                @foreach ($product_categories as $id => $entry)
                                    <option value="{{ $id }}">{{ $entry }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label required" for="sub_sub_category_parent_id">Sub Category</label>
                            <select class="form-select form-select-sm" name="parent_id" id="sub_sub_category_parent_id" data-control="select2" required>
                                <option value="">{{ trans('global.pleaseSelect') }}</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label required" for="sub_sub_category_title">Sub Sub Category Title</label>
                            <input class="form-control form-control-sm" type="text" name="title" id="sub_sub_category_title" required>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select form-select-sm" name="status" data-control="select2">
                                <option value="active" selected>Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary add_product_sub_sub_category_modal_button">Add Sub Sub Category</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.js"></script>
    {{-- dropzone start --}}
    <script>
        Dropzone.options.productImageDropzone = {
            url: '{{ route('admin.products.storeMedia') }}',
            maxFilesize: 100, // MB
            acceptedFiles: '.jpeg,.jpg,.png,.gif',
            maxFiles: 1,
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            params: {
                size: 100,
                width: 4096,
                height: 4096
            },
            success: function(file, response) {
                $('form').find('input[name="product_image"]').remove()
                $('form').append('<input type="hidden" name="product_image" value="' + response.name + '">')
            },
            removedfile: function(file) {
                file.previewElement.remove()
                if (file.status !== 'error') {
                    $('form').find('input[name="product_image"]').remove()
                    this.options.maxFiles = this.options.maxFiles + 1
                }
            },
            init: function() {
                @if (isset($product) && $product->product_image)
                    var file = {!! json_encode($product->product_image) !!}
                    this.options.addedfile.call(this, file)
                    this.options.thumbnail.call(this, file, file.preview ?? file.preview_url)
                    file.previewElement.classList.add('dz-complete')
                    $('form').append('<input type="hidden" name="product_image" value="' + file.file_name + '">')
                    this.options.maxFiles = this.options.maxFiles - 1
                @endif
            },
            error: function(file, response) {
                if ($.type(response) === 'string') {
                    var message = response //dropzone sends it's own error messages in string
                } else {
                    var message = response.errors.file
                }
                file.previewElement.classList.add('dz-error')
                _ref = file.previewElement.querySelectorAll('[data-dz-errormessage]')
                _results = []
                for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                    node = _ref[_i]
                    _results.push(node.textContent = message)
                }

                return _results
            }
        }
    </script>
    {{-- dropzone end --}}

    {{-- add supplier --}}
    <script>
        $(document).on('click', '#open_modal_supplier', function() {
            $('#add_supplier_modal').modal('show');
        });
        $('#add_supplier_modal').on('hidden.bs.modal', function() {
            var form = $(this).find('form');
            form.trigger('reset');
            form.validate().resetForm();
            form.find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
        });

        $("#add_supplier_modal_form").validate({
            rules: {},
            messages: {}
        });


        $(document).on('click', '.add_supplier_modal_button', function() {

            var isValid = false;
            var save_type = $(this).data('save-type');

            if ($("#add_supplier_modal_form").valid()) {

                var formData = new FormData($('#add_supplier_modal_form')[0]);

                $.ajax({
                    url: "{{ route('admin.suppliers.store') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        if (data.success) {
                            $('#add_supplier_modal').modal('hide');
                            $('#supplier_id').append('<option value="' + data.supplier.id + '" selected="selected">' + data.supplier.company_name + '</option>').trigger('change');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = xhr.status + ': ' + xhr.statusText;
                        var response = JSON.parse(xhr.responseText);
                        Swal.fire({
                            text: response.message || 'An error occurred',
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-danger"
                            }
                        });
                    }

                });
            }

        });
    </script>

    {{-- add product sub category / product tag --}}
    <script>
        $("#add_product_tags_modal_form").validate({
            rules: {},
            messages: {}
        });

        $(document).on('click', '.add_product_tags_modal_button', function() {

            var isValid = false;
            var save_type = $(this).data('save-type');

            if ($("#add_product_tags_modal_form").valid()) {

                var formData = new FormData($('#add_product_tags_modal_form')[0]);

                $.ajax({
                    url: "{{ route('admin.product-tags.store') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        if (data.success) {
                            $('#add_product_tags_modal').modal('hide');
                            $('#product_tags_id').append('<option value="' + data.product_tags.id + '" selected="selected">' + data.product_tags.title + '</option>').trigger('change');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = xhr.status + ': ' + xhr.statusText;
                        var response = JSON.parse(xhr.responseText);
                        Swal.fire({
                            text: response.message || 'An error occurred',
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-danger"
                            }
                        });
                    }
                });
            }
        });
    </script>

    <script>
        $(document).on('click', '#open_modal_product_tags', function() {
            $('#add_product_tags_modal').modal('show');
        });
        $('#add_product_tags_modal').on('hidden.bs.modal', function() {
            var form = $(this).find('form');
            form.trigger('reset');
            form.validate().resetForm();
            form.find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
        });

        $(document).on('click', '#open_modal_product_tags', function() {
            $('#add_product_tags_modal').modal('show');
        });
        $('#add_product_tags_modal').on('hidden.bs.modal', function() {
            var form = $(this).find('form');
            form.trigger('reset');
            form.validate().resetForm();
            form.find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
        });
    </script>

    {{-- add product category --}}
    <script>
        $("#modal_product_category_form").validate({
            rules: {},
            messages: {}
        });

        $(document).on('click', '.add_product_category_modal_button', function() {

            var isValid = false;
            var save_type = $(this).data('save-type');

            if ($("#modal_product_category_form").valid()) {

                var formData = new FormData($('#modal_product_category_form')[0]);

                $.ajax({
                    url: "{{ route('admin.product-categories.store') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        if (data.success) {
                            $('#modal_product_category').modal('hide');
                            $('#product_category_id').append('<option value="' + data.product_categories.id + '" selected="selected">' + data.product_categories.title + '</option>').trigger('change');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = xhr.status + ': ' + xhr.statusText;
                        var response = JSON.parse(xhr.responseText);
                        Swal.fire({
                            text: response.message || 'An error occurred',
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-danger"
                            }
                        });
                    }
                });
            }
        });

        // Sub Category Form Submission
        $("#modal_product_sub_category_form").validate({
            rules: {},
            messages: {}
        });

        $(document).on('click', '.add_product_sub_category_modal_button', function() {
            if ($("#modal_product_sub_category_form").valid()) {
                var formData = new FormData($('#modal_product_sub_category_form')[0]);

                $.ajax({
                    url: "{{ route('admin.product-categories.store') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        if (data.success) {
                            $('#modal_product_sub_category').modal('hide');
                            $('#product_sub_category_id').append('<option value="' + data.product_categories.id + '" selected="selected">' + data.product_categories.title + '</option>').trigger('change');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = xhr.status + ': ' + xhr.statusText;
                        var response = JSON.parse(xhr.responseText);
                        Swal.fire({
                            text: response.message || 'An error occurred',
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-danger"
                            }
                        });
                    }
                });
            }
        });

        // Sub Sub Category Form Submission
        $("#modal_product_sub_sub_category_form").validate({
            rules: {},
            messages: {}
        });

        $(document).on('click', '.add_product_sub_sub_category_modal_button', function() {
            if ($("#modal_product_sub_sub_category_form").valid()) {
                var formData = new FormData($('#modal_product_sub_sub_category_form')[0]);

                $.ajax({
                    url: "{{ route('admin.product-categories.store') }}",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        if (data.success) {
                            $('#modal_product_sub_sub_category').modal('hide');
                            $('#product_sub_sub_category_id').append('<option value="' + data.product_categories.id + '" selected="selected">' + data.product_categories.title + '</option>').trigger('change');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = xhr.status + ': ' + xhr.statusText;
                        var response = JSON.parse(xhr.responseText);
                        Swal.fire({
                            text: response.message || 'An error occurred',
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-danger"
                            }
                        });
                    }
                });
            }
        });
    </script>

    <script>
        $(document).on('click', '#open_modal_product_category', function() {
            $('#modal_product_category').modal('show');
        });
        $('#modal_product_category').on('hidden.bs.modal', function() {
            var form = $(this).find('form');
            form.trigger('reset');
            form.validate().resetForm();
            form.find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
        });

        // Sub Category Modal
        $(document).on('click', '#open_modal_product_sub_category', function() {
            $('#modal_product_sub_category').modal('show');
        });
        $('#modal_product_sub_category').on('hidden.bs.modal', function() {
            var form = $(this).find('form');
            form.trigger('reset');
            form.validate().resetForm();
            form.find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
        });

        // Sub Sub Category Modal
        $(document).on('click', '#open_modal_product_sub_sub_category', function() {
            $('#modal_product_sub_sub_category').modal('show');
        });
        $('#modal_product_sub_sub_category').on('hidden.bs.modal', function() {
            var form = $(this).find('form');
            form.trigger('reset');
            form.validate().resetForm();
            form.find('.is-valid, .is-invalid').removeClass('is-valid is-invalid');
        });

        // Dependent Dropdowns
        $(document).ready(function() {
            // Main category change - populate sub categories
            $('#product_category_id').on('change', function() {
                var categoryId = $(this).val();
                var subCategorySelect = $('#product_sub_category_id');
                var subSubCategorySelect = $('#product_sub_sub_category_id');

                // Clear sub categories and sub sub categories
                subCategorySelect.empty().append('<option value="">{{ trans('global.pleaseSelect') }}</option>');
                subSubCategorySelect.empty().append('<option value="">{{ trans('global.pleaseSelect') }}</option>');

                if (categoryId) {
                    $.ajax({
                        url: "{{ route('admin.product-categories.subcategories') }}",
                        type: "GET",
                        data: { parent_id: categoryId },
                        success: function(data) {
                            $.each(data, function(index, subcategory) {
                                subCategorySelect.append('<option value="' + subcategory.id + '">' + subcategory.title + '</option>');
                            });
                        }
                    });
                }
            });

            // Sub category change - populate sub sub categories
            $('#product_sub_category_id').on('change', function() {
                var subCategoryId = $(this).val();
                var subSubCategorySelect = $('#product_sub_sub_category_id');

                // Clear sub sub categories
                subSubCategorySelect.empty().append('<option value="">{{ trans('global.pleaseSelect') }}</option>');

                if (subCategoryId) {
                    $.ajax({
                        url: "{{ route('admin.product-categories.subsubcategories') }}",
                        type: "GET",
                        data: { parent_id: subCategoryId },
                        success: function(data) {
                            $.each(data, function(index, subsubcategory) {
                                subSubCategorySelect.append('<option value="' + subsubcategory.id + '">' + subsubcategory.title + '</option>');
                            });
                        }
                    });
                }
            });

            // Modal dependent dropdowns
            $('#sub_sub_category_main_parent_id').on('change', function() {
                var categoryId = $(this).val();
                var subCategorySelect = $('#sub_sub_category_parent_id');

                // Clear sub categories
                subCategorySelect.empty().append('<option value="">{{ trans('global.pleaseSelect') }}</option>');

                if (categoryId) {
                    $.ajax({
                        url: "{{ route('admin.product-categories.subcategories') }}",
                        type: "GET",
                        data: { parent_id: categoryId },
                        success: function(data) {
                            $.each(data, function(index, subcategory) {
                                subCategorySelect.append('<option value="' + subcategory.id + '">' + subcategory.title + '</option>');
                            });
                        }
                    });
                }
            });

            var toggle_supplier_item = {{ $product->toggle_supplier_item ?? 0 }};
            var toggle_costing_item = {{ $product->toggle_costing_item ?? 0 }};
            // Check if the checkbox is checked or not
            if (toggle_supplier_item) {
                $('#toggle_supplier_item').prop('checked', true);
                $('.supplier-item').show();
            } else {
                $('#toggle_supplier_item').prop('checked', false);
                $('.supplier-item').hide();
            }

            if (toggle_costing_item) {
                $('#toggle_costing_item').prop('checked', true);
                $('.costing-item').show();
            } else {
                $('#toggle_costing_item').prop('checked', false);
                $('.costing-item').hide();
            }


            $('#toggle_supplier_item').change(function() {
                // Check if the checkbox is checked or not
                if ($(this).prop('checked')) {
                    // Show the elements with class 'supplier-item' if checked
                    $('.supplier-item').show();
                } else {
                    // Hide the elements with class 'supplier-item' if unchecked
                    $('.supplier-item').hide();
                }
            });
            $('#toggle_costing_item').change(function() {
                // Check if the checkbox is checked or not
                if ($(this).prop('checked')) {
                    // Show the elements with class 'supplier-item' if checked
                    $('.costing-item').show();
                } else {
                    // Hide the elements with class 'supplier-item' if unchecked
                    $('.costing-item').hide();
                }
            });


        });
        $(document).ready(function() {
            $('#charges_toggle').on('change', function() {
                // Change the value of the input based on the toggle state
                if ($(this).is(':checked')) {
                    $('#charges_type').val('number');
                } else {
                    $('#charges_type').val('percentage');
                }
            });
        });
        $(document).ready(function() {
            $('#shipping_charges_toggle').on('change', function() {
                // Change the value of the input based on the toggle state
                if ($(this).is(':checked')) {
                    $('#shipping_charges_type').val('number');
                } else {
                    $('#shipping_charges_type').val('percentage');
                }
            });
        });
        $(document).ready(function() {
            $('#clearence_type_toggle').on('change', function() {
                // Change the value of the input based on the toggle state
                if ($(this).is(':checked')) {
                    $('#clearence_type').val('number');
                } else {
                    $('#clearence_type').val('percentage');
                }
            });
        });
        $(document).ready(function() {
            $('#duties_type_toggle').on('change', function() {
                // Change the value of the input based on the toggle state
                if ($(this).is(':checked')) {
                    $('#duties_type').val('number');
                } else {
                    $('#duties_type').val('percentage');
                }
            });
        });
    </script>`


<script>
    $(document).ready(function() {

        // 1) Show/hide Rate of Exchange fields
        function toggleRateOfExchangeDiv() {
            if ($('#checkbox_rate_of_exchange').is(':checked')) {
                $('#rate_of_exchange_div').show();
            } else {
                $('#rate_of_exchange_div').hide();
                // If you want to reset them whenever toggled off, uncomment:
                $('#unit_buying_in_currency').val("0.00");
                $('#rate_of_exchange').val("0.00");
            }
            calculateAll();
        }

        $('#checkbox_rate_of_exchange').on('change', toggleRateOfExchangeDiv);
        toggleRateOfExchangeDiv(); // run once on load

        // 2) Generic function to handle %/num toggle
        function setupPercentageNumberToggle(toggleCheckboxId, hiddenTypeFieldId) {
            $('#' + toggleCheckboxId).change(function(){
                if ($(this).is(':checked')) {
                    // If checkbox is checked => treat as "num"
                    $('#' + hiddenTypeFieldId).val('num');
                } else {
                    // If checkbox is unchecked => treat as "percentage"
                    $('#' + hiddenTypeFieldId).val('percentage');
                }
                calculateAll();
            });
        }

        // Hook up all toggles for charges, shipping, clearence, duties, discount
        setupPercentageNumberToggle('charges_toggle', 'charges_type');
        setupPercentageNumberToggle('shipping_charges_toggle', 'shipping_charges_type');
        setupPercentageNumberToggle('clearence_type_toggle', 'clearence_type');
        setupPercentageNumberToggle('duties_type_toggle', 'duties_type');
        setupPercentageNumberToggle('margin_type_toggle', 'margin_type');
        setupPercentageNumberToggle('mark_up_price_toggle', 'mark_up_price_type');
        setupPercentageNumberToggle('discount_rebate_refund_type_toggle', 'discount_rebate_refund_type');

        // 3) Main calculation function
        function calculateAll(){
            // If Rate of Exchange is ON => local = in_currency * exchange
            // Otherwise, we take "unit_buying_local_price" as typed
            let unitInCurrency  = parseFloat($('#unit_buying_in_currency').val())  || 0;
            let rateOfExchange  = parseFloat($('#rate_of_exchange').val())        || 0;
            let localPrice      = parseFloat($('#unit_buying_local_price').val()) || 0;

            if ($('#checkbox_rate_of_exchange').is(':checked')) {
                localPrice = unitInCurrency * rateOfExchange;
                $('#unit_buying_local_price').val(localPrice.toFixed(2));
            }

            // Start netPrice from the local price
            let netPrice = localPrice;

            // Charges
            let chargesVal  = parseFloat($('#charges').val()) || 0;
            let chargesType = $('#charges_type').val();
            if (chargesType === 'percentage') {
                netPrice += (netPrice * (chargesVal/100));
            } else {
                netPrice += chargesVal;
            }

            // Shipping
            let shippingVal  = parseFloat($('#shipping_charges').val()) || 0;
            let shippingType = $('#shipping_charges_type').val();
            if (shippingType === 'percentage') {
                netPrice += (netPrice * (shippingVal/100));
            } else {
                netPrice += shippingVal;
            }

            // Clearence
            let clearenceVal  = parseFloat($('#clearence').val()) || 0;
            let clearenceType = $('#clearence_type').val();
            if (clearenceType === 'percentage') {
                netPrice += (netPrice * (clearenceVal/100));
            } else {
                netPrice += clearenceVal;
            }

            // Duties
            let dutiesVal  = parseFloat($('#duties').val()) || 0;
            let dutiesType = $('#duties_type').val();
            if (dutiesType === 'percentage') {
                netPrice += (netPrice * (dutiesVal/100));
            } else {
                netPrice += dutiesVal;
            }



            // Delivery (always numeric)
            let deliveryVal = parseFloat($('#delivery_charges').val()) || 0;
            netPrice += deliveryVal;

            // Update #net_price
            $('#net_price').val(netPrice.toFixed(2));

            // Discount => new_sale_price
            let discountVal  = parseFloat($('#discount_rebate_refund').val()) || 0;
            let discountType = $('#discount_rebate_refund_type').val();
            let newSalePrice = netPrice;

            if (discountType === 'percentage') {
                newSalePrice = newSalePrice - (newSalePrice * (discountVal/100));
            } else {
                newSalePrice = newSalePrice - discountVal;
            }

            $('#new_sale_price').val(newSalePrice.toFixed(2));

            let finalSalePrice = newSalePrice;

            // margin
            let marginVal  = parseFloat($('#margin').val()) || 0;
            let marginType = $('#margin_type').val();
            if (marginType === 'percentage') {
                finalSalePrice += (finalSalePrice * (marginVal/100));
            } else {
                finalSalePrice += marginVal;
            }

            // Mark Up Price
            let markUpVal  = parseFloat($('#mark_up_price').val()) || 0;
            let markUpType = $('#mark_up_price_type').val();
            if (markUpType === 'percentage') {
                finalSalePrice += (finalSalePrice * (markUpVal/100));
            } else {
                finalSalePrice += markUpVal;
            }

            $('#final_sale_price').val(finalSalePrice.toFixed(2));
            // $('#unit_sales_price').val(finalSalePrice.toFixed(2));
        }

        // 4) Whenever an input changes => recalc
        $('#unit_buying_in_currency, #rate_of_exchange, #unit_buying_local_price, ' +
          '#charges, #shipping_charges, #clearence, #duties, ' +
          '#delivery_charges, #discount_rebate_refund,' +
          '#margin, #mark_up_price'
        ).on('keyup change', function(){
            calculateAll();
        });

        // On page load => do initial calc
        calculateAll();

    });
</script>
@endsection
