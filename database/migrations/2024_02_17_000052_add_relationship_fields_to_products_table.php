<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRelationshipFieldsToProductsTable extends Migration
{
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->unsignedBigInteger('product_category_id')->nullable();
            $table->foreign('product_category_id', 'product_category_fk_9371684')->references('id')->on('product_categories');
            $table->unsignedBigInteger('product_sub_category_id')->nullable();
            $table->foreign('product_sub_category_id', 'product_sub_category_fk_9371685')->references('id')->on('product_categories');
            $table->unsignedBigInteger('product_sub_sub_category_id')->nullable();
            $table->foreign('product_sub_sub_category_id', 'product_sub_sub_category_fk_9371686')->references('id')->on('product_categories');
            $table->unsignedBigInteger('supplier_id')->nullable();
            $table->foreign('supplier_id', 'supplier_fk_9433276')->references('id')->on('suppliers');
            $table->unsignedBigInteger('team_id')->nullable();
            $table->foreign('team_id', 'team_fk_9371625')->references('id')->on('teams');
        });
    }
}
