<?php

use App\Http\Controllers\Admin\CommandLogController;
use App\Http\Controllers\Admin\CustomerController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\QuotationItemController;
use App\Http\Controllers\Admin\vatReportController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\testSchedule;

Route::redirect('/', '/login');
Route::get('/home', function () {
    if (session('status')) {
        return redirect()->route('admin.home')->with('status', session('status'));
    }

    return redirect()->route('admin.home');
});

Auth::routes();

Route::group(['prefix' => 'admin', 'as' => 'admin.', 'namespace' => 'Admin', 'middleware' => ['auth']], function () {
    Route::get('/', 'HomeController@index')->name('home');
    Route::get('/dashboard-menu', 'HomeController@dashBoardMenu')->name('home.dashboard-menu');


    //Dashboard Filters
    Route::get('/getquote','HomeController@getQuote')->name('getquote');
    Route::get('/getinvoice','HomeController@getInvoice')->name('getinvoice');
    Route::get('/debtors','HomeController@getDebtors')->name('getdebtors');
    Route::get('/creditors','HomeController@getCreditors')->name('getcreditors');

    // Contributions
    Route::delete('contributions/destroy', 'ContributionsController@massDestroy')->name('contributions.massDestroy');
    Route::post('contributions/parse-csv-import', 'ContributionsController@parseCsvImport')->name('contributions.parseCsvImport');
    Route::post('contributions/process-csv-import', 'ContributionsController@processCsvImport')->name('contributions.processCsvImport');
    Route::resource('contributions', 'ContributionsController');
    Route::post('contributions/get-value', 'ContributionsController@getValue')->name('contributions.getValue');


    // Employee Contributions
    Route::delete('employee-contributions/destroy', 'EmployeeContributionsController@massDestroy')->name('employee-contributions.massDestroy');
    Route::post('employee-contributions/parse-csv-import', 'EmployeeContributionsController@parseCsvImport')->name('employee-contributions.parseCsvImport');
    Route::post('employee-contributions/process-csv-import', 'EmployeeContributionsController@processCsvImport')->name('employee-contributions.processCsvImport');
    Route::resource('employee-contributions', 'EmployeeContributionsController');

    // Terms And Conditions
    Route::delete('terms-and-conditions/destroy', 'TermsAndConditionsController@massDestroy')->name('terms-and-conditions.massDestroy');
    Route::post('terms-and-conditions/media', 'TermsAndConditionsController@storeMedia')->name('terms-and-conditions.storeMedia');
    Route::post('terms-and-conditions/ckmedia', 'TermsAndConditionsController@storeCKEditorImages')->name('terms-and-conditions.storeCKEditorImages');
    Route::post('terms-and-conditions/parse-csv-import', 'TermsAndConditionsController@parseCsvImport')->name('terms-and-conditions.parseCsvImport');
    Route::post('terms-and-conditions/process-csv-import', 'TermsAndConditionsController@processCsvImport')->name('terms-and-conditions.processCsvImport');
    Route::resource('terms-and-conditions', 'TermsAndConditionsController');

    // getTermsAndConditions
    Route::post('get-terms-and-conditions', 'TermsAndConditionsController@getTermsAndConditions')->name('terms-and-conditions.getTermsAndConditions');

    // Reminder
    Route::delete('reminders/destroy', 'ReminderController@massDestroy')->name('reminders.massDestroy');
    Route::post('reminders/parse-csv-import', 'ReminderController@parseCsvImport')->name('reminders.parseCsvImport');
    Route::post('reminders/process-csv-import', 'ReminderController@processCsvImport')->name('reminders.processCsvImport');
    Route::resource('reminders', 'ReminderController');

    // Project
    Route::delete('projects/destroy', 'ProjectController@massDestroy')->name('projects.massDestroy');
    Route::post('projects/parse-csv-import', 'ProjectController@parseCsvImport')->name('projects.parseCsvImport');
    Route::post('projects/process-csv-import', 'ProjectController@processCsvImport')->name('projects.processCsvImport');
    Route::resource('projects', 'ProjectController');
    Route::get('projects/workspace/{project}', 'ProjectController@workspace')->name('projects.workspaceListing');


    // Project Category
    Route::delete('project-categories/destroy', 'ProjectCategoryController@massDestroy')->name('project-categories.massDestroy');
    Route::post('project-categories/parse-csv-import', 'ProjectCategoryController@parseCsvImport')->name('project-categories.parseCsvImport');
    Route::post('project-categories/process-csv-import', 'ProjectCategoryController@processCsvImport')->name('project-categories.processCsvImport');
    Route::resource('project-categories', 'ProjectCategoryController');

    // Expense
    Route::delete('expenses/destroy', 'ExpenseController@massDestroy')->name('expenses.massDestroy');
    Route::post('expenses/media', 'ExpenseController@storeMedia')->name('expenses.storeMedia');
    Route::post('expenses/ckmedia', 'ExpenseController@storeCKEditorImages')->name('expenses.storeCKEditorImages');
    Route::post('expenses/parse-csv-import', 'ExpenseController@parseCsvImport')->name('expenses.parseCsvImport');
    Route::post('expenses/process-csv-import', 'ExpenseController@processCsvImport')->name('expenses.processCsvImport');
    Route::resource('expenses', 'ExpenseController');

    // Employee
    Route::delete('employees/destroy', 'EmployeeController@massDestroy')->name('employees.massDestroy');
    Route::post('employees/media', 'EmployeeController@storeMedia')->name('employees.storeMedia');
    Route::post('employees/ckmedia', 'EmployeeController@storeCKEditorImages')->name('employees.storeCKEditorImages');
    Route::post('employees/parse-csv-import', 'EmployeeController@parseCsvImport')->name('employees.parseCsvImport');
    Route::post('employees/process-csv-import', 'EmployeeController@processCsvImport')->name('employees.processCsvImport');
    Route::resource('employees', 'EmployeeController');

    // Employee Relationships
    Route::get('employees/allowance/{employee}', 'EmployeeController@allowance')->name('employees.allowance');
    Route::get('employees/address/{employee}', 'EmployeeController@address')->name('employees.address');
    Route::get('employees/commission/{employee}', 'EmployeeController@commission')->name('employees.commission');
    Route::get('employees/deduction/{employee}', 'EmployeeController@deduction')->name('employees.deduction');
    Route::get('employees/contribution/{employee}', 'EmployeeController@contribution')->name('employees.contribution');
    Route::get('employees/bonus/{employee}', 'EmployeeController@bonus')->name('employees.bonus');
    Route::get('employees/pay-slip/{employee}', 'EmployeeController@paySlip')->name('employees.pay-slip');
    Route::get('employees/basic-salary/{employee}', 'EmployeeController@basicSalary')->name('employees.basic-salary');
    Route::get('employees/bank-info/{employee}', 'EmployeeController@bankInfo')->name('employees.bank-info');
    // request-leave
    Route::get('employees/request-leave/{employee}', 'EmployeeController@requestLeave')->name('employees.request-leave');
    Route::get('employees/leave/{employee}', 'EmployeeController@leave')->name('employees.leave');
    Route::get('employees/comment/{employee}', 'EmployeeController@comment')->name('employees.comment');
    Route::get('employees/document/{employee}', 'EmployeeController@document')->name('employees.document');



    // Time Tracker
    Route::get('time-tracker', 'EmployeeController@timeTracker')->name('timeTracker');

    // Employee Allowance
    Route::delete('employee-allowances/destroy', 'EmployeeAllowanceController@massDestroy')->name('employee-allowances.massDestroy');
    Route::post('employee-allowances/parse-csv-import', 'EmployeeAllowanceController@parseCsvImport')->name('employee-allowances.parseCsvImport');
    Route::post('employee-allowances/process-csv-import', 'EmployeeAllowanceController@processCsvImport')->name('employee-allowances.processCsvImport');
    Route::resource('employee-allowances', 'EmployeeAllowanceController');

    // Employee Leave
    Route::delete('employee-leaves/destroy', 'EmployeeLeaveController@massDestroy')->name('employee-leaves.massDestroy');
    Route::post('employee-leaves/media', 'EmployeeLeaveController@storeMedia')->name('employee-leaves.storeMedia');
    Route::post('employee-leaves/ckmedia', 'EmployeeLeaveController@storeCKEditorImages')->name('employee-leaves.storeCKEditorImages');
    Route::post('employee-leaves/parse-csv-import', 'EmployeeLeaveController@parseCsvImport')->name('employee-leaves.parseCsvImport');
    Route::post('employee-leaves/process-csv-import', 'EmployeeLeaveController@processCsvImport')->name('employee-leaves.processCsvImport');
    // Route::resource('employee-leaves', 'EmployeeLeaveController');
    Route::resource('employee-leaves', 'EmployeeLeaveController')->parameters([
        'employee-leaves' => 'employee_leafe'
    ]);

    // Supplier Quotation
    Route::delete('supplier-quotations/destroy', 'SupplierQuotationController@massDestroy')->name('supplier-quotations.massDestroy');
    Route::post('supplier-quotations/media', 'SupplierQuotationController@storeMedia')->name('supplier-quotations.storeMedia');
    Route::post('supplier-quotations/ckmedia', 'SupplierQuotationController@storeCKEditorImages')->name('supplier-quotations.storeCKEditorImages');
    Route::post('supplier-quotations/parse-csv-import', 'SupplierQuotationController@parseCsvImport')->name('supplier-quotations.parseCsvImport');
    Route::post('supplier-quotations/process-csv-import', 'SupplierQuotationController@processCsvImport')->name('supplier-quotations.processCsvImport');
    Route::resource('supplier-quotations', 'SupplierQuotationController');

    // Leave
    Route::delete('leaves/destroy', 'LeaveController@massDestroy')->name('leaves.massDestroy');
    Route::post('leaves/parse-csv-import', 'LeaveController@parseCsvImport')->name('leaves.parseCsvImport');
    Route::post('leaves/process-csv-import', 'LeaveController@processCsvImport')->name('leaves.processCsvImport');
    Route::resource('leaves', 'LeaveController');

    // Employee Bonus
    Route::delete('employee-bonus/destroy', 'EmployeeBonusController@massDestroy')->name('employee-bonus.massDestroy');
    Route::post('employee-bonus/parse-csv-import', 'EmployeeBonusController@parseCsvImport')->name('employee-bonus.parseCsvImport');
    Route::post('employee-bonus/process-csv-import', 'EmployeeBonusController@processCsvImport')->name('employee-bonus.processCsvImport');
    Route::resource('employee-bonus', 'EmployeeBonusController');

    // Expenses Category
    Route::delete('expenses-categories/destroy', 'ExpensesCategoryController@massDestroy')->name('expenses-categories.massDestroy');
    Route::post('expenses-categories/parse-csv-import', 'ExpensesCategoryController@parseCsvImport')->name('expenses-categories.parseCsvImport');
    Route::post('expenses-categories/process-csv-import', 'ExpensesCategoryController@processCsvImport')->name('expenses-categories.processCsvImport');
    Route::resource('expenses-categories', 'ExpensesCategoryController');

    // Permissions
    Route::delete('permissions/destroy', 'PermissionsController@massDestroy')->name('permissions.massDestroy');
    Route::post('permissions/parse-csv-import', 'PermissionsController@parseCsvImport')->name('permissions.parseCsvImport');
    Route::post('permissions/process-csv-import', 'PermissionsController@processCsvImport')->name('permissions.processCsvImport');
    Route::resource('permissions', 'PermissionsController');

    // Roles
    Route::delete('roles/destroy', 'RolesController@massDestroy')->name('roles.massDestroy');
    Route::post('roles/parse-csv-import', 'RolesController@parseCsvImport')->name('roles.parseCsvImport');
    Route::post('roles/process-csv-import', 'RolesController@processCsvImport')->name('roles.processCsvImport');
    Route::resource('roles', 'RolesController');

    // Plans
    Route::delete('plans/destroy', 'PlansController@massDestroy')->name('plans.massDestroy');
    Route::resource('plans', 'PlansController');

    // Features
    Route::delete('features/destroy', 'FeaturesController@massDestroy')->name('features.massDestroy');
    Route::resource('features', 'FeaturesController');

    // Users
    Route::delete('users/destroy', 'UsersController@massDestroy')->name('users.massDestroy');
    Route::post('users/media', 'UsersController@storeMedia')->name('users.storeMedia');
    Route::post('users/ckmedia', 'UsersController@storeCKEditorImages')->name('users.storeCKEditorImages');
    Route::post('users/parse-csv-import', 'UsersController@parseCsvImport')->name('users.parseCsvImport');
    Route::post('users/process-csv-import', 'UsersController@processCsvImport')->name('users.processCsvImport');
    Route::resource('users', 'UsersController');

    //updateAvatar
    Route::post('users/update-avatar/{user}', 'UsersController@updateAvatar')->name('users.updateAvatar');

    // User relationship
    Route::get('users/detail/{user}', 'UsersController@detail')->name('users.detail');
    Route::get('users/address/{user}', 'UsersController@address')->name('users.address');
    Route::get('users/profile/{user}', 'UsersController@profile')->name('users.profile');


    // Accounting
    Route::delete('accountings/destroy', 'AccountingController@massDestroy')->name('accountings.massDestroy');
    Route::resource('accountings', 'AccountingController');

    // Customer
    Route::delete('customers/destroy', 'CustomerController@massDestroy')->name('customers.massDestroy');
    Route::post('customers/media', 'CustomerController@storeMedia')->name('customers.storeMedia');
    Route::post('customers/ckmedia', 'CustomerController@storeCKEditorImages')->name('customers.storeCKEditorImages');
    Route::post('customers/parse-csv-import', 'CustomerController@parseCsvImport')->name('customers.parseCsvImport');
    Route::post('customers/process-csv-import', 'CustomerController@processCsvImport')->name('customers.processCsvImport');
    Route::post('/customers/get-modal-form', 'CustomerController@getModalForm')->name('customer.getModalForm');
    Route::post('/customers/get-edit-modal-form', 'CustomerController@getEditModalForm')->name('customer.getEditModalForm');
    Route::resource('customers', 'CustomerController');

    // customer relationship
    Route::get('customers/detail/{customer}', 'CustomerController@detail')->name('customers.detail');
    Route::get('customers/address/{customer}', 'CustomerController@address')->name('customers.address');
    Route::get('customers/contact/{customer}', 'CustomerController@contact')->name('customers.contact');
    Route::get('customers/bank/{customer}', 'CustomerController@bank')->name('customers.bank');
    Route::get('customers/terms/{customer}', 'CustomerController@terms')->name('customers.terms');
    Route::get('customers/social/{customer}', 'CustomerController@social')->name('customers.social');
    Route::get('customers/other/{customer}', 'CustomerController@other')->name('customers.other');
    Route::post('customers/contact-persons', 'CustomerController@getContactPersons')->name('customers.contact-persons');

    // Country
    Route::delete('countries/destroy', 'CountryController@massDestroy')->name('countries.massDestroy');
    Route::post('countries/parse-csv-import', 'CountryController@parseCsvImport')->name('countries.parseCsvImport');
    Route::post('countries/process-csv-import', 'CountryController@processCsvImport')->name('countries.processCsvImport');
    Route::resource('countries', 'CountryController');

    // Audit Logs
    Route::resource('audit-logs', 'AuditLogsController', ['except' => ['create', 'store', 'edit', 'update', 'destroy']]);

    // Team
    Route::delete('teams/destroy', 'TeamController@massDestroy')->name('teams.massDestroy');
    Route::post('teams/media', 'TeamController@storeMedia')->name('teams.storeMedia');
    Route::post('teams/ckmedia', 'TeamController@storeCKEditorImages')->name('teams.storeCKEditorImages');
    Route::post('teams/parse-csv-import', 'TeamController@parseCsvImport')->name('teams.parseCsvImport');
    Route::post('teams/process-csv-import', 'TeamController@processCsvImport')->name('teams.processCsvImport');
    Route::resource('teams', 'TeamController');

    // Customer Contact
    Route::delete('customer-contacts/destroy', 'CustomerContactController@massDestroy')->name('customer-contacts.massDestroy');
    Route::post('customer-contacts/parse-csv-import', 'CustomerContactController@parseCsvImport')->name('customer-contacts.parseCsvImport');
    Route::post('customer-contacts/process-csv-import', 'CustomerContactController@processCsvImport')->name('customer-contacts.processCsvImport');
    Route::resource('customer-contacts', 'CustomerContactController');

    // Customer Addresses
    Route::delete('customer-addresses/destroy', 'CustomerAddressesController@massDestroy')->name('customer-addresses.massDestroy');
    Route::post('customer-addresses/parse-csv-import', 'CustomerAddressesController@parseCsvImport')->name('customer-addresses.parseCsvImport');
    Route::post('customer-addresses/process-csv-import', 'CustomerAddressesController@processCsvImport')->name('customer-addresses.processCsvImport');
    Route::resource('customer-addresses', 'CustomerAddressesController');

    // Customer Contact Person
    Route::delete('customer-contact-people/destroy', 'CustomerContactPersonController@massDestroy')->name('customer-contact-people.massDestroy');
    Route::post('customer-contact-people/parse-csv-import', 'CustomerContactPersonController@parseCsvImport')->name('customer-contact-people.parseCsvImport');
    Route::post('customer-contact-people/process-csv-import', 'CustomerContactPersonController@processCsvImport')->name('customer-contact-people.processCsvImport');
    Route::resource('customer-contact-people', 'CustomerContactPersonController');

    // Customer Bank Account
    Route::delete('customer-bank-accounts/destroy', 'CustomerBankAccountController@massDestroy')->name('customer-bank-accounts.massDestroy');
    Route::post('customer-bank-accounts/parse-csv-import', 'CustomerBankAccountController@parseCsvImport')->name('customer-bank-accounts.parseCsvImport');
    Route::post('customer-bank-accounts/process-csv-import', 'CustomerBankAccountController@processCsvImport')->name('customer-bank-accounts.processCsvImport');
    Route::resource('customer-bank-accounts', 'CustomerBankAccountController');

    // Invoice
    Route::delete('invoices/destroy', 'InvoiceController@massDestroy')->name('invoices.massDestroy');
    Route::post('invoices/media', 'InvoiceController@storeMedia')->name('invoices.storeMedia');
    Route::post('invoices/ckmedia', 'InvoiceController@storeCKEditorImages')->name('invoices.storeCKEditorImages');
    Route::post('invoices/parse-csv-import', 'InvoiceController@parseCsvImport')->name('invoices.parseCsvImport');
    Route::post('invoices/process-csv-import', 'InvoiceController@processCsvImport')->name('invoices.processCsvImport');
    Route::get('invoices/draft', 'InvoiceController@draft')->name('invoices.draft');
    Route::get('invoices/duplicate/{invoice}', 'InvoiceController@duplicate')->name('invoices.duplicate');
    Route::post('invoices/duplicate/confirmed', 'InvoiceController@duplicateConfirmed')->name('invoices.duplicate.confirmed');
    Route::get('invoices/home/<USER>', 'InvoiceController@home')->name('invoices.home');
    Route::get('invoices/delivery-note-pdf/{invoice}', 'InvoiceController@generateDeliveryNotePDF')->name('invoices.generate.delivery.note.pdf');
    Route::get('/invoice-pdf/{invoice_id}', 'InvoiceController@generatePDF')->name('generate.invoice.pdf');
    Route::get('invoices/send-download/{invoice}', 'InvoiceController@sendDownload')->name('invoices.send.download');
    Route::resource('invoices', 'InvoiceController');

    // Supplier Invoice
    Route::delete('supplier-invoice/destroy', 'SupplierInvoiceController@massDestroy')->name('supplier-invoice.massDestroy');
    Route::post('supplier-invoice/media', 'SupplierInvoiceController@storeMedia')->name('supplier-invoice.storeMedia');
    Route::post('supplier-invoice/ckmedia', 'SupplierInvoiceController@storeCKEditorImages')->name('supplier-invoice.storeCKEditorImages');
    Route::post('supplier-invoice/parse-csv-import', 'SupplierInvoiceController@parseCsvImport')->name('supplier-invoice.parseCsvImport');
    Route::post('supplier-invoice/process-csv-import', 'SupplierInvoiceController@processCsvImport')->name('supplier-invoice.processCsvImport');
    Route::get('supplier-invoice/draft', 'SupplierInvoiceController@draft')->name('supplier-invoice.draft');
    Route::get('supplier-invoice/duplicate/{supplierInvoice}', 'SupplierInvoiceController@duplicate')->name('supplier-invoice.duplicate');
    Route::post('supplier-invoice/duplicate/confirmed', 'SupplierInvoiceController@duplicateConfirmed')->name('supplier-invoice.duplicate.confirmed');
    Route::get('supplier-invoice/home/<USER>', 'SupplierInvoiceController@home')->name('supplier-invoice.home');

    Route::get('supplier-invoice/delivery-note-pdf/{invoice}', 'SupplierInvoiceController@generateDeliveryNotePDF')->name('supplier-invoice.generate.delivery.note.pdf');
    Route::get('/supplier-invoice-pdf/{invoice_id}', 'SupplierInvoiceController@generatePDF')->name('generate.supplier-invoice.pdf');
    Route::get('supplier-invoice/send-download/{invoice}', 'SupplierInvoiceController@sendDownload')->name('supplier-invoice.send.download');
    Route::resource('supplier-invoice', 'SupplierInvoiceController');

    // Supplier Invoice Item
    Route::delete('supplierInvoiceItem/destroy', 'SupplierInvoiceItemController@massDestroy')->name('supplierInvoiceItem.massDestroy');
    Route::post('supplierInvoiceItem/media', 'SupplierInvoiceItemController@storeMedia')->name('supplierInvoiceItem.storeMedia');
    Route::post('supplierInvoiceItem/ckmedia', 'SupplierInvoiceItemController@storeCKEditorImages')->name('supplierInvoiceItem.storeCKEditorImages');
    Route::post('supplierInvoiceItem/parse-csv-import', 'SupplierInvoiceItemController@parseCsvImport')->name('supplierInvoiceItem.parseCsvImport');
    Route::post('supplierInvoiceItem/process-csv-import', 'SupplierInvoiceItemController@processCsvImport')->name('supplierInvoiceItem.processCsvImport');
    Route::post('supplierInvoiceItem/get-customer-detail', 'SupplierInvoiceItemController@getCustomerDetail')->name('supplierInvoiceItem.getSupplierDetail');
    Route::resource('supplierInvoiceItem', 'SupplierInvoiceItemController');

      //Supplier Invoice Payment
      Route::delete('invoice-payments/destroy', 'InvoicePaymentController@massDestroy')->name('invoice-payments.massDestroy');
      Route::post('invoice-payments/media', 'InvoicePaymentController@storeMedia')->name('invoice-payments.storeMedia');
      Route::post('invoice-payments/ckmedia', 'InvoicePaymentController@storeCKEditorImages')->name('invoice-payments.storeCKEditorImages');
      Route::post('invoice-payments/parse-csv-import', 'InvoicePaymentController@parseCsvImport')->name('invoice-payments.parseCsvImport');
      Route::post('invoice-payments/process-csv-import', 'InvoicePaymentController@processCsvImport')->name('invoice-payments.processCsvImport');

      Route::resource('invoice-payments', 'InvoicePaymentController');


    // Payment Method
    Route::delete('payment-methods/destroy', 'PaymentMethodController@massDestroy')->name('payment-methods.massDestroy');
    Route::post('payment-methods/parse-csv-import', 'PaymentMethodController@parseCsvImport')->name('payment-methods.parseCsvImport');
    Route::post('payment-methods/process-csv-import', 'PaymentMethodController@processCsvImport')->name('payment-methods.processCsvImport');
    Route::resource('payment-methods', 'PaymentMethodController');

    // Payment Condition
    Route::delete('payment-conditions/destroy', 'PaymentConditionController@massDestroy')->name('payment-conditions.massDestroy');
    Route::post('payment-conditions/parse-csv-import', 'PaymentConditionController@parseCsvImport')->name('payment-conditions.parseCsvImport');
    Route::post('payment-conditions/process-csv-import', 'PaymentConditionController@processCsvImport')->name('payment-conditions.processCsvImport');
    Route::resource('payment-conditions', 'PaymentConditionController');

    // Invoice Item
    Route::delete('invoice-items/destroy', 'InvoiceItemController@massDestroy')->name('invoice-items.massDestroy');
    Route::post('invoice-items/media', 'InvoiceItemController@storeMedia')->name('invoice-items.storeMedia');
    Route::post('invoice-items/ckmedia', 'InvoiceItemController@storeCKEditorImages')->name('invoice-items.storeCKEditorImages');
    Route::post('invoice-items/parse-csv-import', 'InvoiceItemController@parseCsvImport')->name('invoice-items.parseCsvImport');
    Route::post('invoice-items/process-csv-import', 'InvoiceItemController@processCsvImport')->name('invoice-items.processCsvImport');
    Route::resource('invoice-items', 'InvoiceItemController');

    // Invoice Payment
    Route::delete('invoice-payments/destroy', 'InvoicePaymentController@massDestroy')->name('invoice-payments.massDestroy');
    Route::post('invoice-payments/media', 'InvoicePaymentController@storeMedia')->name('invoice-payments.storeMedia');
    Route::post('invoice-payments/ckmedia', 'InvoicePaymentController@storeCKEditorImages')->name('invoice-payments.storeCKEditorImages');
    Route::post('invoice-payments/parse-csv-import', 'InvoicePaymentController@parseCsvImport')->name('invoice-payments.parseCsvImport');
    Route::post('invoice-payments/process-csv-import', 'InvoicePaymentController@processCsvImport')->name('invoice-payments.processCsvImport');
    Route::resource('invoice-payments', 'InvoicePaymentController');

    // Supplier
    Route::delete('suppliers/destroy', 'SupplierController@massDestroy')->name('suppliers.massDestroy');
    Route::post('suppliers/media', 'SupplierController@storeMedia')->name('suppliers.storeMedia');
    Route::post('suppliers/ckmedia', 'SupplierController@storeCKEditorImages')->name('suppliers.storeCKEditorImages');
    Route::post('suppliers/parse-csv-import', 'SupplierController@parseCsvImport')->name('suppliers.parseCsvImport');
    Route::post('suppliers/process-csv-import', 'SupplierController@processCsvImport')->name('suppliers.processCsvImport');
    Route::post('/suppliers/get-modal-form', 'SupplierController@getModalForm')->name('suppliers.getModalForm');
    Route::post('/suppliers/get-edit-modal-form', 'SupplierController@getEditModalForm')->name('suppliers.getEditModalForm');
    Route::resource('suppliers', 'SupplierController');

    // Supplier relationship
    Route::get('suppliers/detail/{supplier}', 'SupplierController@detail')->name('suppliers.detail');
    Route::get('suppliers/address/{supplier}', 'SupplierController@address')->name('suppliers.address');
    Route::get('suppliers/contact/{supplier}', 'SupplierController@contact')->name('suppliers.contact');
    Route::get('suppliers/bank/{supplier}', 'SupplierController@bank')->name('suppliers.bank');
    Route::get('suppliers/terms/{supplier}', 'SupplierController@terms')->name('suppliers.terms');
    Route::get('suppliers/social/{supplier}', 'SupplierController@social')->name('suppliers.social');

    // Supplier Contact
    Route::delete('supplier-contacts/destroy', 'SupplierContactController@massDestroy')->name('supplier-contacts.massDestroy');
    Route::post('supplier-contacts/parse-csv-import', 'SupplierContactController@parseCsvImport')->name('supplier-contacts.parseCsvImport');
    Route::post('supplier-contacts/process-csv-import', 'SupplierContactController@processCsvImport')->name('supplier-contacts.processCsvImport');
    Route::resource('supplier-contacts', 'SupplierContactController');

    // Supplier Addresses
    Route::delete('supplier-addresses/destroy', 'SupplierAddressesController@massDestroy')->name('supplier-addresses.massDestroy');
    Route::post('supplier-addresses/parse-csv-import', 'SupplierAddressesController@parseCsvImport')->name('supplier-addresses.parseCsvImport');
    Route::post('supplier-addresses/process-csv-import', 'SupplierAddressesController@processCsvImport')->name('supplier-addresses.processCsvImport');
    Route::resource('supplier-addresses', 'SupplierAddressesController');

    // Supplier Contact Person
    Route::delete('supplier-contact-people/destroy', 'SupplierContactPersonController@massDestroy')->name('supplier-contact-people.massDestroy');
    Route::post('supplier-contact-people/parse-csv-import', 'SupplierContactPersonController@parseCsvImport')->name('supplier-contact-people.parseCsvImport');
    Route::post('supplier-contact-people/process-csv-import', 'SupplierContactPersonController@processCsvImport')->name('supplier-contact-people.processCsvImport');
    Route::resource('supplier-contact-people', 'SupplierContactPersonController');

    // Supplier Bank Account
    Route::delete('supplier-bank-accounts/destroy', 'SupplierBankAccountController@massDestroy')->name('supplier-bank-accounts.massDestroy');
    Route::post('supplier-bank-accounts/parse-csv-import', 'SupplierBankAccountController@parseCsvImport')->name('supplier-bank-accounts.parseCsvImport');
    Route::post('supplier-bank-accounts/process-csv-import', 'SupplierBankAccountController@processCsvImport')->name('supplier-bank-accounts.processCsvImport');
    Route::resource('supplier-bank-accounts', 'SupplierBankAccountController');

    // Product
    Route::delete('products/destroy', 'ProductController@massDestroy')->name('products.massDestroy');
    Route::post('products/media', 'ProductController@storeMedia')->name('products.storeMedia');
    Route::post('products/ckmedia', 'ProductController@storeCKEditorImages')->name('products.storeCKEditorImages');
    Route::post('products/parse-csv-import', 'ProductController@parseCsvImport')->name('products.parseCsvImport');
    Route::post('products/process-csv-import', 'ProductController@processCsvImport')->name('products.processCsvImport');
    Route::get('products/process', 'ProductController@process')->name('products.process');
    Route::get('products/plan-validate', 'ProductController@planValidate')->name('products.plan.validate');
    Route::resource('products', 'ProductController');

    // get items
    Route::post('products/get-items', 'ProductController@fetchItemCodes')->name('products.getItems');
    Route::post('products/search-items', 'ProductController@searchItemCodes')->name('products.searchItems');
    Route::post('products/detail', 'ProductController@itemDetail')->name('products.getProductDetails');



    // Service
    Route::delete('services/destroy', 'ServiceController@massDestroy')->name('services.massDestroy');
    Route::post('services/parse-csv-import', 'ServiceController@parseCsvImport')->name('services.parseCsvImport');
    Route::post('services/process-csv-import', 'ServiceController@processCsvImport')->name('services.processCsvImport');
    Route::resource('services', 'ServiceController');

    // Product Category
    Route::delete('product-categories/destroy', 'ProductCategoryController@massDestroy')->name('product-categories.massDestroy');
    Route::post('product-categories/parse-csv-import', 'ProductCategoryController@parseCsvImport')->name('product-categories.parseCsvImport');
    Route::post('product-categories/process-csv-import', 'ProductCategoryController@processCsvImport')->name('product-categories.processCsvImport');
    Route::get('product-categories/subcategories', 'ProductCategoryController@getSubcategories')->name('product-categories.subcategories');
    Route::get('product-categories/subsubcategories', 'ProductCategoryController@getSubSubcategories')->name('product-categories.subsubcategories');
    Route::resource('product-categories', 'ProductCategoryController');

    // Service Category
    Route::delete('service-categories/destroy', 'ServiceCategoryController@massDestroy')->name('service-categories.massDestroy');
    Route::post('service-categories/parse-csv-import', 'ServiceCategoryController@parseCsvImport')->name('service-categories.parseCsvImport');
    Route::post('service-categories/process-csv-import', 'ServiceCategoryController@processCsvImport')->name('service-categories.processCsvImport');
    Route::resource('service-categories', 'ServiceCategoryController');

    // Product Tag
    Route::delete('product-tags/destroy', 'ProductTagController@massDestroy')->name('product-tags.massDestroy');
    Route::post('product-tags/parse-csv-import', 'ProductTagController@parseCsvImport')->name('product-tags.parseCsvImport');
    Route::post('product-tags/process-csv-import', 'ProductTagController@processCsvImport')->name('product-tags.processCsvImport');
    Route::resource('product-tags', 'ProductTagController');

    // Service Tag
    Route::delete('service-tags/destroy', 'ServiceTagController@massDestroy')->name('service-tags.massDestroy');
    Route::post('service-tags/parse-csv-import', 'ServiceTagController@parseCsvImport')->name('service-tags.parseCsvImport');
    Route::post('service-tags/process-csv-import', 'ServiceTagController@processCsvImport')->name('service-tags.processCsvImport');
    Route::resource('service-tags', 'ServiceTagController');

    // Bank
    Route::delete('banks/destroy', 'BankController@massDestroy')->name('banks.massDestroy');
    Route::post('banks/parse-csv-import', 'BankController@parseCsvImport')->name('banks.parseCsvImport');
    Route::post('banks/process-csv-import', 'BankController@processCsvImport')->name('banks.processCsvImport');
    Route::resource('banks', 'BankController');

    // Link To
    Route::post('link-tos/get-drawer-content', 'LinkToController@getDrawerContent')->name('link-tos.getDrawerContent');
    Route::post('link-tos/get-select', 'LinkToController@getSelect')->name('link-tos.getSelect');
    Route::post('link-tos/save-link', 'LinkToController@saveLink')->name('link-tos.saveLink');
    Route::post('link-tos/delete-link', 'LinkToController@deleteLink')->name('link-tos.deleteLink');

    // Quotation
    Route::delete('quotations/destroy', 'QuotationController@massDestroy')->name('quotations.massDestroy');
    Route::post('quotations/media', 'QuotationController@storeMedia')->name('quotations.storeMedia');
    Route::post('quotations/ckmedia', 'QuotationController@storeCKEditorImages')->name('quotations.storeCKEditorImages');
    Route::post('quotations/parse-csv-import', 'QuotationController@parseCsvImport')->name('quotations.parseCsvImport');
    Route::post('quotations/process-csv-import', 'QuotationController@processCsvImport')->name('quotations.processCsvImport');
    Route::get('quotations/draft', 'QuotationController@draft')->name('quotations.draft');
    Route::get('quotations/send-download/{quotation}', 'QuotationController@sendDownload')->name('quotations.send.download');

    Route::get('quotations/make-invoice/{quotation}', 'QuotationController@makeInvoice')->name('quotations.make.invoice');
    Route::get('quotations/make-proforma-invoice/{quotation}', 'QuotationController@makeProformaInvoice')->name('quotations.make.proforma.invoice');
    Route::get('quotations/make-multiple-invoice/{quotation}', 'QuotationController@makeMultipleInvoice')->name('quotations.make.multiple.invoice');
    Route::get('quotations/multiple-invoice/{quotation}', 'QuotationController@multipleInvoice')->name('quotations.multiple');
    Route::get('quotations/duplicate/{quotation}', 'QuotationController@duplicate')->name('quotations.duplicate');
    Route::post('quotations/duplicate/confirmed', 'QuotationController@duplicateConfirmed')->name('quotations.duplicate.confirmed');
    Route::post('quotations/make-invoice/confirmed', 'QuotationController@makeInvoiceConfirmed')->name('quotations.make.invoice.confirmed');

    Route::post('quotations/make-multiple-invoice/confirmed', 'QuotationController@makeMultipleInvoiceConfirmed')->name('quotations.make.multiple.invoice.confirmed');
    Route::post('quotations/make-proforma-invoice/confirmed', 'QuotationController@makeProformaInvoiceConfirmed')->name('quotations.make.proforma.invoice.confirmed');
    Route::get('quotations/home/<USER>', 'QuotationController@home')->name('quotations.home');
    Route::get('/quotation-pdf/{quotation_id}', 'QuotationController@generatePDF')->name('generate.quotation.pdf');
    Route::resource('quotations', 'QuotationController');
    // Route::post('quotations/draft', 'QuotationController@draft')->name('quotations.draft');

    // Quotation Item
    Route::delete('quotation-items/destroy', 'QuotationItemController@massDestroy')->name('quotation-items.massDestroy');
    Route::post('quotation-items/media', 'QuotationItemController@storeMedia')->name('quotation-items.storeMedia');
    Route::post('quotation-items/ckmedia', 'QuotationItemController@storeCKEditorImages')->name('quotation-items.storeCKEditorImages');
    Route::post('quotation-items/parse-csv-import', 'QuotationItemController@parseCsvImport')->name('quotation-items.parseCsvImport');
    Route::post('quotation-items/process-csv-import', 'QuotationItemController@processCsvImport')->name('quotation-items.processCsvImport');
    Route::resource('quotation-items', 'QuotationItemController');

    // Quotation Item Group
    Route::delete('quotation-item-groups/destroy', 'QuotationItemGroupController@massDestroy')->name('quotation-item-groups.massDestroy');
    Route::post('quotation-item-groups/parse-csv-import', 'QuotationItemGroupController@parseCsvImport')->name('quotation-item-groups.parseCsvImport');
    Route::post('quotation-item-groups/process-csv-import', 'QuotationItemGroupController@processCsvImport')->name('quotation-item-groups.processCsvImport');
    Route::resource('quotation-item-groups', 'QuotationItemGroupController');

    // Define the route for creating a quotation
    Route::post('/admin/customers/store', 'CustomerController@store')->name('admin.customers.store');

    // Define the route for creating a currency which is in country
    Route::post('/admin/countries/store', 'CountryController@store')->name('admin.countries.store');

    // Define the route for creating payment condition
    Route::post('/admin/payment-conditions/store', 'PaymentConditionController@store')->name('admin.payment-conditions.store');

    // Define the route for creating payment method
    Route::post('/admin/payment-methods/store', 'PaymentMethodController@store')->name('admin.payment-methods.store');


    Route::get('global-search', 'GlobalSearchController@search')->name('globalSearch');
    Route::get('team-members', 'TeamMembersController@index')->name('team-members.index');
    Route::post('team-members', 'TeamMembersController@invite')->name('team-members.invite');

    // Job Tile
    Route::delete('job-tiles/destroy', 'JobTileController@massDestroy')->name('job-tiles.massDestroy');
    Route::post('job-tiles/parse-csv-import', 'JobTileController@parseCsvImport')->name('job-tiles.parseCsvImport');
    Route::post('job-tiles/process-csv-import', 'JobTileController@processCsvImport')->name('job-tiles.processCsvImport');
    Route::resource('job-tiles', 'JobTileController');

    // Department
    Route::delete('departments/destroy', 'DepartmentController@massDestroy')->name('departments.massDestroy');
    Route::post('departments/parse-csv-import', 'DepartmentController@parseCsvImport')->name('departments.parseCsvImport');
    Route::post('departments/process-csv-import', 'DepartmentController@processCsvImport')->name('departments.processCsvImport');
    Route::resource('departments', 'DepartmentController');

    // Genre
    Route::delete('genres/destroy', 'GenreController@massDestroy')->name('genres.massDestroy');
    Route::post('genres/parse-csv-import', 'GenreController@parseCsvImport')->name('genres.parseCsvImport');
    Route::post('genres/process-csv-import', 'GenreController@processCsvImport')->name('genres.processCsvImport');
    Route::resource('genres', 'GenreController');

    Route::post('quotation-items/get-modal-items', 'QuotationItemController@getModalItems')->name('quotation-items.getModalItems');
    Route::post('quotation-items/get-quotation-items', 'QuotationItemController@getQuotationItems')->name('quotation-items.getQuotationItems');
    Route::post('quotation-items/get-section-html', 'QuotationItemController@getSectionHtml')->name('quotation-items.getSectionHtml');
    Route::post('quotation-items/get-customer-detail', 'QuotationItemController@getCustomerDetail')->name('quotation-items.getCustomerDetail');
    Route::post('quotation-items/get-supplier-detail', 'QuotationItemController@getSupplierDetail')->name('quotation-items.getSupplierDetail');
    // Proforma Invoice
    Route::delete('proforma-invoices/destroy', 'ProformaInvoiceController@massDestroy')->name('proforma-invoices.massDestroy');
    Route::post('proforma-invoices/media', 'ProformaInvoiceController@storeMedia')->name('proforma-invoices.storeMedia');
    Route::post('proforma-invoices/ckmedia', 'ProformaInvoiceController@storeCKEditorImages')->name('proforma-invoices.storeCKEditorImages');
    Route::post('proforma-invoices/parse-csv-import', 'ProformaInvoiceController@parseCsvImport')->name('proforma-invoices.parseCsvImport');
    Route::post('proforma-invoices/process-csv-import', 'ProformaInvoiceController@processCsvImport')->name('proforma-invoices.processCsvImport');
    Route::get('proforma-invoices/draft', 'ProformaInvoiceController@draft')->name('proforma-invoices.draft');
    Route::get('proforma-invoices/duplicate/{proformaInvoice}', 'ProformaInvoiceController@duplicate')->name('proforma-invoices.duplicate');
    Route::post('proforma-invoices/duplicate/confirmed', 'ProformaInvoiceController@duplicateConfirmed')->name('proforma-invoices.duplicate.confirmed');
    Route::get('proforma-invoices/home/<USER>', 'ProformaInvoiceController@home')->name('proforma-invoices.home');
    Route::get('proforma-invoices/delivery-note-pdf/{proformaInvoice}', 'ProformaInvoiceController@generateDeliveryNotePDF')->name('proforma-invoices.generate.delivery.note.pdf');
    Route::get('/proforma-invoice-pdf/{proforma_invoice_id}', 'ProformaInvoiceController@generatePDF')->name('generate.proforma-invoice.pdf');
    Route::get('proforma-invoices/send-download/{proformaInvoice}', 'ProformaInvoiceController@sendDownload')->name('proforma-invoices.send.download');
    Route::get('proforma-invoices/make-invoice/{proformaInvoice}', 'ProformaInvoiceController@makeInvoice')->name('proforma-invoices.make.invoice');
    Route::post('proforma-invoices/make-invoice/confirmed', 'ProformaInvoiceController@makeInvoiceConfirmed')->name('proforma-invoices.make.invoice.confirmed');
    Route::resource('proforma-invoices', 'ProformaInvoiceController');

    // Proforma Invoice Item
    Route::delete('proforma-invoice-items/destroy', 'ProformaInvoiceItemController@massDestroy')->name('proforma-invoice-items.massDestroy');
    Route::post('proforma-invoice-items/media', 'ProformaInvoiceItemController@storeMedia')->name('proforma-invoice-items.storeMedia');
    Route::post('proforma-invoice-items/ckmedia', 'ProformaInvoiceItemController@storeCKEditorImages')->name('proforma-invoice-items.storeCKEditorImages');
    Route::post('proforma-invoice-items/parse-csv-import', 'ProformaInvoiceItemController@parseCsvImport')->name('proforma-invoice-items.parseCsvImport');
    Route::post('proforma-invoice-items/process-csv-import', 'ProformaInvoiceItemController@processCsvImport')->name('proforma-invoice-items.processCsvImport');
    Route::resource('proforma-invoice-items', 'ProformaInvoiceItemController');

    // Purchase
    Route::delete('purchases/destroy', 'PurchaseController@massDestroy')->name('purchases.massDestroy');
    Route::post('purchases/media', 'PurchaseController@storeMedia')->name('purchases.storeMedia');
    Route::post('purchases/ckmedia', 'PurchaseController@storeCKEditorImages')->name('purchases.storeCKEditorImages');
    Route::post('purchases/parse-csv-import', 'PurchaseController@parseCsvImport')->name('purchases.parseCsvImport');
    Route::post('purchases/process-csv-import', 'PurchaseController@processCsvImport')->name('purchases.processCsvImport');
    Route::get('purchases/draft', 'PurchaseController@draft')->name('purchases.draft');

    Route::get('purchases/duplicate/{purchase}', 'PurchaseController@duplicate')->name('purchases.duplicate');
    Route::post('purchases/duplicate/confirmed', 'PurchaseController@duplicateConfirmed')->name('purchases.duplicate.confirmed');
    Route::get('purchases/home/<USER>', 'PurchaseController@home')->name('purchases.home');
    Route::get('purchases/delivery-note-pdf/{purchase}', 'PurchaseController@generateDeliveryNotePDF')->name('purchases.generate.delivery.note.pdf');
    Route::get('/purchase-pdf/{purchase_id}', 'PurchaseController@generatePDF')->name('generate.purchase.pdf');
    Route::get('purchases/send-download/{purchase}', 'PurchaseController@sendDownload')->name('purchases.send.download');
    Route::get('purchases/make-invoice/{purchase}', 'PurchaseController@makeInvoice')->name('purchases.make.invoice');
    Route::post('purchase/make-invoice/confirmed', 'PurchaseController@makeInvoiceConfirmed')->name('purchases.make.invoice.confirmed');

    Route::resource('purchases', 'PurchaseController');

    // Purchase Order Item
    Route::delete('purchase-order-items/destroy', 'PurchaseOrderItemController@massDestroy')->name('purchase-order-items.massDestroy');
    Route::post('purchase-order-items/media', 'PurchaseOrderItemController@storeMedia')->name('purchase-order-items.storeMedia');
    Route::post('purchase-order-items/ckmedia', 'PurchaseOrderItemController@storeCKEditorImages')->name('purchase-order-items.storeCKEditorImages');
    Route::post('purchase-order-items/parse-csv-import', 'PurchaseOrderItemController@parseCsvImport')->name('purchase-order-items.parseCsvImport');
    Route::post('purchase-order-items/process-csv-import', 'PurchaseOrderItemController@processCsvImport')->name('purchase-order-items.processCsvImport');
    Route::resource('purchase-order-items', 'PurchaseOrderItemController');

    // Store
    Route::delete('stores/destroy', 'StoreController@massDestroy')->name('stores.massDestroy');
    Route::post('stores/parse-csv-import', 'StoreController@parseCsvImport')->name('stores.parseCsvImport');
    Route::post('stores/process-csv-import', 'StoreController@processCsvImport')->name('stores.processCsvImport');
    Route::resource('stores', 'StoreController');

    // Stage
    Route::delete('stages/destroy', 'StageController@massDestroy')->name('stages.massDestroy');
    Route::post('stages/parse-csv-import', 'StageController@parseCsvImport')->name('stages.parseCsvImport');
    Route::post('stages/process-csv-import', 'StageController@processCsvImport')->name('stages.processCsvImport');
    Route::resource('stages', 'StageController');
    Route::post('stages/edit-label', 'StageController@editLabel')->name('stages.editLabel');
    Route::post('stages/update-label', 'StageController@updateLabel')->name('stages.updateLabel');


    // Board

    Route::delete('boards/destroy', 'BoardController@massDestroy')->name('boards.massDestroy');
    Route::post('boards/parse-csv-import', 'BoardController@parseCsvImport')->name('boards.parseCsvImport');
    Route::post('boards/process-csv-import', 'BoardController@processCsvImport')->name('boards.processCsvImport');
    Route::resource('boards', 'BoardController');

    // Group
    Route::delete('groups/destroy', 'GroupController@massDestroy')->name('groups.massDestroy');
    Route::post('groups/parse-csv-import', 'GroupController@parseCsvImport')->name('groups.parseCsvImport');
    Route::post('groups/process-csv-import', 'GroupController@processCsvImport')->name('groups.processCsvImport');
    Route::resource('groups', 'GroupController');
    // renameGroupTitle
    Route::post('groups/rename-group-title', 'GroupController@renameGroupTitle')->name('groups.renameGroupTitle');

    // Priority
    Route::delete('priorities/destroy', 'PriorityController@massDestroy')->name('priorities.massDestroy');
    Route::post('priorities/parse-csv-import', 'PriorityController@parseCsvImport')->name('priorities.parseCsvImport');
    Route::post('priorities/process-csv-import', 'PriorityController@processCsvImport')->name('priorities.processCsvImport');
    Route::resource('priorities', 'PriorityController');

    // Board Item
    Route::delete('board-items/destroy', 'BoardItemController@massDestroy')->name('board-items.massDestroy');
    Route::post('board-items/parse-csv-import', 'BoardItemController@parseCsvImport')->name('board-items.parseCsvImport');
    Route::post('board-items/process-csv-import', 'BoardItemController@processCsvImport')->name('board-items.processCsvImport');
    Route::resource('board-items', 'BoardItemController');
    Route::post('board-items/create-board-item', 'BoardItemController@createBoardItem')->name('board-items.createBoardItem');
    Route::post('board-items/update-board-item', 'BoardItemController@updateBoardItem')->name('board-items.updateBoardItem');
    Route::post('board-items/get-people', 'BoardItemController@getPeople')->name('board-items.getPeople');
    Route::post('board-items/add-people', 'BoardItemController@addPeople')->name('board-items.addPeople');
    Route::post('board-items/delete-people', 'BoardItemController@deletePeople')->name('board-items.deletePeople');
    Route::post('board-items/get-attachment', 'BoardItemController@getAttachment')->name('board-items.getAttachment');
    Route::post('board-items/save-attachment', 'BoardItemController@saveAttachment')->name('board-items.saveAttachment');
    Route::post('board-items/update-row-order', 'BoardItemController@updateRowOrder')->name('board-items.updateRowOrder');
    // delete_board_item
    Route::post('board-items/delete-board-item', 'BoardItemController@deleteBoardItem')->name('board-items.deleteBoardItem');


    // Board Custom Field
    Route::delete('board-custom-fields/destroy', 'BoardCustomFieldController@massDestroy')->name('board-custom-fields.massDestroy');
    Route::post('board-custom-fields/parse-csv-import', 'BoardCustomFieldController@parseCsvImport')->name('board-custom-fields.parseCsvImport');
    Route::post('board-custom-fields/process-csv-import', 'BoardCustomFieldController@processCsvImport')->name('board-custom-fields.processCsvImport');
    Route::resource('board-custom-fields', 'BoardCustomFieldController');
    Route::post('board-custom-fields/update-board-custom-field-order', 'BoardCustomFieldController@updateBoardCustomFieldOrder')->name('board-custom-fields.updateBoardCustomFieldOrder');

    // Board Custom Field Value
    Route::delete('board-custom-field-values/destroy', 'BoardCustomFieldValueController@massDestroy')->name('board-custom-field-values.massDestroy');
    Route::post('board-custom-field-values/parse-csv-import', 'BoardCustomFieldValueController@parseCsvImport')->name('board-custom-field-values.parseCsvImport');
    Route::post('board-custom-field-values/process-csv-import', 'BoardCustomFieldValueController@processCsvImport')->name('board-custom-field-values.processCsvImport');
    Route::resource('board-custom-field-values', 'BoardCustomFieldValueController');

    // UpdateBoardCustomFieldValue
    Route::post('update-board-custom-field-values', 'BoardCustomFieldValueController@updateBoardCustomFieldValue')->name('board-custom-field-values.update-board-custom-field-values');

    // Board Item Message
    Route::delete('board-item-messages/destroy', 'BoardItemMessageController@massDestroy')->name('board-item-messages.massDestroy');
    Route::post('board-item-messages/media', 'BoardItemMessageController@storeMedia')->name('board-item-messages.storeMedia');
    Route::post('board-item-messages/ckmedia', 'BoardItemMessageController@storeCKEditorImages')->name('board-item-messages.storeCKEditorImages');
    Route::post('board-item-messages/parse-csv-import', 'BoardItemMessageController@parseCsvImport')->name('board-item-messages.parseCsvImport');
    Route::post('board-item-messages/process-csv-import', 'BoardItemMessageController@processCsvImport')->name('board-item-messages.processCsvImport');
    Route::resource('board-item-messages', 'BoardItemMessageController');

    // Board View
    Route::delete('board-views/destroy', 'BoardViewController@massDestroy')->name('board-views.massDestroy');
    Route::post('board-views/parse-csv-import', 'BoardViewController@parseCsvImport')->name('board-views.parseCsvImport');
    Route::post('board-views/process-csv-import', 'BoardViewController@processCsvImport')->name('board-views.processCsvImport');
    Route::resource('board-views', 'BoardViewController');

    // Dealtracker
    Route::get('deal-tracker', 'DealTrackerController@index')->name('deal.tracker');
    Route::get('grid', 'DealTrackerController@grid')->name('deal.tracker');
    Route::get('tabulator', 'DealTrackerController@tabulator')->name('deal.tabulator');

    // Report
    Route::get('reports/debtor', 'ReportController@debtor')->name('reports.debtor');
    Route::get('reports/creditor', 'ReportController@creditor')->name('reports.creditor');
    Route::get('reports/statement-of-account', 'ReportController@statementOfAccount')->name('reports.statement-of-account');

    //Export Rport
    Route::get('reports/export-users', 'ReportController@exportDebtors')->name('reports.exportDebtors');
    Route::post('reports/get-report', 'ReportController@getReport')->name('reports.get-report');
    Route::post('reports/generate-pdf', 'ReportController@generatePdf')->name('reports.generate.pdf');
    Route::post('reports/get-customer-search-option', 'ReportController@getCustomerSearchOption')->name('reports.get-customer-search-option');

    //vatReport
    Route::get('reports/vat-invoice-report', 'vatReportController@invoiceReport')->name('reports.vat-invoice-report');
    Route::get('reports/vat-supplier-report', 'vatReportController@supplierReport')->name('reports.vat-supplier-report');
    Route::get('reports/vat-report', 'vatReportController@index')->name('reports.vat-report');
    Route::get('reports/get-vat-report', 'vatReportController@getVatReport')->name('reports.get-vatreport');
    Route::get('reports/export-vat-report-xlsx', 'vatReportController@vatReportXlsx')->name('reports.vat-report-xlsx-export');

    // Deduction
    Route::delete('deductions/destroy', 'DeductionController@massDestroy')->name('deductions.massDestroy');
    Route::post('deductions/parse-csv-import', 'DeductionController@parseCsvImport')->name('deductions.parseCsvImport');
    Route::post('deductions/process-csv-import', 'DeductionController@processCsvImport')->name('deductions.processCsvImport');
    Route::resource('deductions', 'DeductionController');
    Route::post('deductions/get-value', 'DeductionController@getValue')->name('deductions.getValue');

    // Employee Deduction
    Route::delete('employee-deductions/destroy', 'EmployeeDeductionController@massDestroy')->name('employee-deductions.massDestroy');
    Route::post('employee-deductions/parse-csv-import', 'EmployeeDeductionController@parseCsvImport')->name('employee-deductions.parseCsvImport');
    Route::post('employee-deductions/process-csv-import', 'EmployeeDeductionController@processCsvImport')->name('employee-deductions.processCsvImport');
    Route::resource('employee-deductions', 'EmployeeDeductionController');

    // Bonus
    Route::delete('bonus/destroy', 'BonusController@massDestroy')->name('bonus.massDestroy');
    Route::post('bonus/parse-csv-import', 'BonusController@parseCsvImport')->name('bonus.parseCsvImport');
    Route::post('bonus/process-csv-import', 'BonusController@processCsvImport')->name('bonus.processCsvImport');
    Route::resource('bonus', 'BonusController');
    Route::post('bonus/get-value', 'BonusController@getValue')->name('bonus.getValue');

    // Allowance
    Route::delete('allowances/destroy', 'AllowanceController@massDestroy')->name('allowances.massDestroy');
    Route::post('allowances/parse-csv-import', 'AllowanceController@parseCsvImport')->name('allowances.parseCsvImport');
    Route::post('allowances/process-csv-import', 'AllowanceController@processCsvImport')->name('allowances.processCsvImport');
    Route::resource('allowances', 'AllowanceController');
    Route::post('allowances/get-value', 'AllowanceController@getValue')->name('allowances.getValue');

    // Board Item Comment
    Route::delete('board-item-comments/destroy', 'BoardItemCommentController@massDestroy')->name('board-item-comments.massDestroy');
    Route::post('board-item-comments/parse-csv-import', 'BoardItemCommentController@parseCsvImport')->name('board-item-comments.parseCsvImport');
    Route::post('board-item-comments/process-csv-import', 'BoardItemCommentController@processCsvImport')->name('board-item-comments.processCsvImport');
    Route::resource('board-item-comments', 'BoardItemCommentController');
    // allcomment
    Route::post('board-item-comments/all-comment', 'BoardItemCommentController@allComment')->name('board-item-comments.allComment');
    // addComment
    Route::post('board-item-comments/add-comment', 'BoardItemCommentController@addComment')->name('board-item-comments.addComment');

    // Workspace
    Route::delete('workspaces/destroy', 'WorkspaceController@massDestroy')->name('workspaces.massDestroy');
    Route::post('workspaces/parse-csv-import', 'WorkspaceController@parseCsvImport')->name('workspaces.parseCsvImport');
    Route::post('workspaces/process-csv-import', 'WorkspaceController@processCsvImport')->name('workspaces.processCsvImport');
    Route::resource('workspaces', 'WorkspaceController');

    // Employee Payslip
    Route::delete('employee-payslips/destroy', 'EmployeePayslipController@massDestroy')->name('employee-payslips.massDestroy');
    Route::post('employee-payslips/parse-csv-import', 'EmployeePayslipController@parseCsvImport')->name('employee-payslips.parseCsvImport');
    Route::post('employee-payslips/process-csv-import', 'EmployeePayslipController@processCsvImport')->name('employee-payslips.processCsvImport');
    Route::resource('employee-payslips', 'EmployeePayslipController');
    Route::post('employee-payslips/get-employee-detail', 'EmployeePayslipController@getEmployeeDetail')->name('employee-payslips.getEmployeeDetail');
    Route::get('/employee-payslips-pdf/{employee_payslip_id}', 'EmployeePayslipController@generatePDF')->name('employee-payslips.generatePDF');

    // Payslip Allowance
    Route::delete('payslip-allowances/destroy', 'PayslipAllowanceController@massDestroy')->name('payslip-allowances.massDestroy');
    Route::post('payslip-allowances/parse-csv-import', 'PayslipAllowanceController@parseCsvImport')->name('payslip-allowances.parseCsvImport');
    Route::post('payslip-allowances/process-csv-import', 'PayslipAllowanceController@processCsvImport')->name('payslip-allowances.processCsvImport');
    Route::resource('payslip-allowances', 'PayslipAllowanceController');

    // Payslip Deduction
    Route::delete('payslip-deductions/destroy', 'PayslipDeductionController@massDestroy')->name('payslip-deductions.massDestroy');
    Route::post('payslip-deductions/parse-csv-import', 'PayslipDeductionController@parseCsvImport')->name('payslip-deductions.parseCsvImport');
    Route::post('payslip-deductions/process-csv-import', 'PayslipDeductionController@processCsvImport')->name('payslip-deductions.processCsvImport');
    Route::resource('payslip-deductions', 'PayslipDeductionController');

    // Payslip Contribution
    Route::delete('payslip-contributions/destroy', 'PayslipContributionController@massDestroy')->name('payslip-contributions.massDestroy');
    Route::post('payslip-contributions/parse-csv-import', 'PayslipContributionController@parseCsvImport')->name('payslip-contributions.parseCsvImport');
    Route::post('payslip-contributions/process-csv-import', 'PayslipContributionController@processCsvImport')->name('payslip-contributions.processCsvImport');
    Route::resource('payslip-contributions', 'PayslipContributionController');

    // Payslip Bonus
    Route::delete('payslip-bonus/destroy', 'PayslipBonusController@massDestroy')->name('payslip-bonus.massDestroy');
    Route::post('payslip-bonus/parse-csv-import', 'PayslipBonusController@parseCsvImport')->name('payslip-bonus.parseCsvImport');
    Route::post('payslip-bonus/process-csv-import', 'PayslipBonusController@processCsvImport')->name('payslip-bonus.processCsvImport');
    Route::resource('payslip-bonus', 'PayslipBonusController');

    // Commission
    Route::delete('commissions/destroy', 'CommissionController@massDestroy')->name('commissions.massDestroy');
    Route::post('commissions/parse-csv-import', 'CommissionController@parseCsvImport')->name('commissions.parseCsvImport');
    Route::post('commissions/process-csv-import', 'CommissionController@processCsvImport')->name('commissions.processCsvImport');
    Route::resource('commissions', 'CommissionController');

    // Employee Commission
    Route::delete('employee-commissions/destroy', 'EmployeeCommissionController@massDestroy')->name('employee-commissions.massDestroy');
    Route::post('employee-commissions/parse-csv-import', 'EmployeeCommissionController@parseCsvImport')->name('employee-commissions.parseCsvImport');
    Route::post('employee-commissions/process-csv-import', 'EmployeeCommissionController@processCsvImport')->name('employee-commissions.processCsvImport');
    Route::resource('employee-commissions', 'EmployeeCommissionController');

    // Payslip Commission
    Route::delete('payslip-commissions/destroy', 'PayslipCommissionController@massDestroy')->name('payslip-commissions.massDestroy');
    Route::post('payslip-commissions/parse-csv-import', 'PayslipCommissionController@parseCsvImport')->name('payslip-commissions.parseCsvImport');
    Route::post('payslip-commissions/process-csv-import', 'PayslipCommissionController@processCsvImport')->name('payslip-commissions.processCsvImport');
    Route::resource('payslip-commissions', 'PayslipCommissionController');

    // Expense Type
    Route::delete('expense-types/destroy', 'ExpenseTypeController@massDestroy')->name('expense-types.massDestroy');
    Route::post('expense-types/parse-csv-import', 'ExpenseTypeController@parseCsvImport')->name('expense-types.parseCsvImport');
    Route::post('expense-types/process-csv-import', 'ExpenseTypeController@processCsvImport')->name('expense-types.processCsvImport');
    Route::resource('expense-types', 'ExpenseTypeController');

    // Employee Documents
    Route::delete('employee-documents/destroy', 'EmployeeDocumentsController@massDestroy')->name('employee-documents.massDestroy');
    Route::post('employee-documents/media', 'EmployeeDocumentsController@storeMedia')->name('employee-documents.storeMedia');
    Route::post('employee-documents/ckmedia', 'EmployeeDocumentsController@storeCKEditorImages')->name('employee-documents.storeCKEditorImages');
    Route::resource('employee-documents', 'EmployeeDocumentsController');

    // Employee Comment
    Route::delete('employee-comments/destroy', 'EmployeeCommentController@massDestroy')->name('employee-comments.massDestroy');
    Route::post('employee-comments/parse-csv-import', 'EmployeeCommentController@parseCsvImport')->name('employee-comments.parseCsvImport');
    Route::post('employee-comments/process-csv-import', 'EmployeeCommentController@processCsvImport')->name('employee-comments.processCsvImport');
    Route::resource('employee-comments', 'EmployeeCommentController');

    // Price List
    Route::delete('price-lists/destroy', 'PriceListController@massDestroy')->name('price-lists.massDestroy');
    Route::post('price-lists/media', 'PriceListController@storeMedia')->name('price-lists.storeMedia');
    Route::post('price-lists/ckmedia', 'PriceListController@storeCKEditorImages')->name('price-lists.storeCKEditorImages');
    Route::post('price-lists/parse-csv-import', 'PriceListController@parseCsvImport')->name('price-lists.parseCsvImport');
    Route::post('price-lists/process-csv-import', 'PriceListController@processCsvImport')->name('price-lists.processCsvImport');
    Route::resource('price-lists', 'PriceListController');

    // Command Logs
    Route::resource('command-logs', 'CommandLogController');
    Route::post('command-logs/toggle-status/{id}', 'CommandLogController@toggleStatus')->name('command-logs.toggle-status');
    Route::post('command-logs/run-command/{id}', 'CommandLogController@runCommand')->name('command-logs.run-command');



});
Route::group(['prefix' => 'profile', 'as' => 'profile.', 'namespace' => 'Auth', 'middleware' => ['auth']], function () {
    // Change password
    if (file_exists(app_path('Http/Controllers/Auth/ChangePasswordController.php'))) {
        Route::get('password', 'ChangePasswordController@edit')->name('password.edit');
        Route::post('password', 'ChangePasswordController@update')->name('password.update');
        Route::post('profile', 'ChangePasswordController@updateProfile')->name('password.updateProfile');
        Route::post('profile/destroy', 'ChangePasswordController@destroy')->name('password.destroyProfile');
    }
});

route::get('/webapp/login', 'App\Http\Controllers\Webapp\webappController@login')->name('web-login');
route::get('/webapp/upload', 'App\Http\Controllers\Webapp\webappController@upload')->name('web-upload');



Route::get('/process-invoices', [testSchedule::class, 'handle']);


Route::get('/phpinfo', function () {
    phpinfo();
});
